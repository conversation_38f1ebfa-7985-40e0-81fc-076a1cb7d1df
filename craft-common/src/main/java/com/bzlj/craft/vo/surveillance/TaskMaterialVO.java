package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TaskMaterialVO implements Serializable {
    @ComponentProperty(label = "物料id", display = false, path = "material.materialId")
    private String materialId;

    @ComponentProperty(label = "牌号", order = 1, path = "material.brand")
    private String brand;

    /**
     * 锭型
     */
    @ComponentProperty(label = "锭型", order = 2, path = "material.ingotType")
    private String ingotType;

    @ComponentProperty(label = "炉号", order = 3, path = "material.heatNumber")
    private String heatNumber;

    /**
     * 锭号
     */
    @ComponentProperty(label = "锭号", order = 4, path = "material.ingotNumber")
    private String ingotNumber;
}