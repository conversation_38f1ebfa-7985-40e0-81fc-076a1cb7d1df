package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-05-07 11:22
 */
@Data
public class ProcessScoreVO {
    @ComponentProperty(label = "id", display = false, path = "id")
    private String id;

    @ComponentProperty(label = "工步id", path = "stepId",display = false, order = 1)
    private String stepId;

    @ComponentProperty(label = "工步名称", path = "stepName",order = 2)
    private String stepName;

    @ComponentProperty(label = "更新人员", path = "updateUser.fullName",order = 3)
    private String updateUserName;

    @ComponentProperty(label = "最近一次更新时间", path = "updateAt",order = 4)
    private LocalDateTime updateAt;


    @ComponentProperty(label = "状态", path = "enableFlag", order = 5)
    private String enableFlag;

    @ComponentProperty(label = "规则详情",display = false, path = "ruleDetail",order = 6)
    private Map<String, Object> ruleDetail;
}
