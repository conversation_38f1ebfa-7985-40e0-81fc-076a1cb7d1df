package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 任务统计VO模型
 * @date 2025-03-14 15:23
 */
@Data
public class TaskStatisticsVO {

    @ComponentProperty(label = "总任务数", path = "total")
    private Long total;          // 总任务数
    @ComponentProperty(label = "进行中任务", path = "inProgressCount",order = 1)
    private Long inProgressCount;// 进行中数量
    @ComponentProperty(label = "未开始任务", path = "notStartedCount",order = 2)
    private Long notStartedCount;// 未开始数量
    @ComponentProperty(label = "已完成任务", path = "completedCount",order = 3)
    private Long completedCount; // 已完成数量
}
