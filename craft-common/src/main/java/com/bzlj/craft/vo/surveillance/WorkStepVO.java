package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import com.bzlj.base.enums.DataSourceType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-17 17:15
 */
@Data
public class WorkStepVO {
    @ComponentProperty(label = "执行工步id", display = false, path = "id")
    private String id;

    @ComponentProperty(label = "工步id", display = false,order = 1, path = "step.id")
    private String stepId;

    @ComponentProperty(label = "工步编号", order = 2,display = false, path = "step.stepCode")
    private String stepCode;

    @ComponentProperty(label = "执行工步名称", order = 3,display = false, path = "workStepName")
    private String workStepName;

    @ComponentProperty(label = "工步状态", order = 4, path = "statusCode.itemCode",display = false,dataSourceType = DataSourceType.DICT, dictCode = "TASK_STATUS")
    private String statusCode;

    @ComponentProperty(label = "开始", order = 5, path = "startTime")
    private LocalDateTime startTime;

    @ComponentProperty(label = "结束", order = 6, path = "endTime")
    private LocalDateTime endTime;

    @ComponentProperty(label = "排序",display = false, order = 7, path = "workStepOrder")
    private Integer workStepOrder;

}
