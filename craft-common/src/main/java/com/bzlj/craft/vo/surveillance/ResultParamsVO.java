package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-17 17:15
 */
@Data
public class ResultParamsVO {
    @ComponentProperty(label = "工步id", display = false, path = "id")
    private String id;

    @ComponentProperty(label = "工步编号",display = false, order = 1, path = "stepCode")
    private String stepCode;

    @ComponentProperty(label = "工步", order = 2, path = "workStepName")
    private String workStepName;

    @ComponentProperty(label = "工艺参数", order = 3, path = "stepParam")
    private String stepParam;

    @ComponentProperty(label = "参数标准值", order = 4, path = "standard")
    private String standard;

    @ComponentProperty(label = "参数实际值", order = 5, path = "actualValue")
    private Object actualValue;

    @ComponentProperty(label = "排序",display = false, order = 6, path = "stepOrder")
    private Integer stepOrder;

}
