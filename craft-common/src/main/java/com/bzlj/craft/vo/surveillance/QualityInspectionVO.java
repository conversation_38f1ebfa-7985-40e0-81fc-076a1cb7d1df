package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-11 17:53
 */
@Data
public class QualityInspectionVO {

    @ComponentProperty(label = "检验时间", path = "inspectionId")
    private LocalDateTime inspectTime;

    @ComponentProperty(label = "物料编号", order = 1, path = "material.materialCode")
    private String materialCode;

    @ComponentProperty(label = "样号", order = 2, path = "sampleNumber")
    private String sampleNumber;

    @ComponentProperty(label = "炉号", order = 3, path = "heatNumber")
    private String heatNumber;
}
