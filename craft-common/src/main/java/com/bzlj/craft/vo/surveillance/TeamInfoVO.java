package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-17 17:15
 */
@Data
public class TeamInfoVO {
    @ComponentProperty(label = "班组id", display = false, path = "logId")
    private String teamId;

    @ComponentProperty(label = "交接班次", order = 1, path = "num")
    private Integer num ;

    @ComponentProperty(label = "班组名称", order = 2, path = "team.teamName")
    private String teamName;

    @ComponentProperty(label = "组长", order = 3, path = "groupLeader")
    private String groupLeader;

    @ComponentProperty(label = "组员", order = 4, path = "members")
    private String members;

    @ComponentProperty(label = "交接班时间", order = 5, path = "handoverTime")
    private LocalDateTime handoverTime;

}
