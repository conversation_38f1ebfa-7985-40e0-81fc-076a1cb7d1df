package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProduceReportLoadMaterialVO {

    @ComponentProperty(label = "上料记录id", display = false, path = "id")
    private String id;

    /**
     * 上料时间
     */
    @ComponentProperty(label = "上料时间", order = 1, path = "createTime",range = true)
    private LocalDateTime createTime;

    /**
     * 上料人
     */
    @ComponentProperty(label = "员工", order = 2, path = "createBy")
    private String createByUserName;

    /**
     * 物料号
     */
    @ComponentProperty(label = "物料号", order = 3, path = "material.materialCode")
    private String materialCode;

    /**
     * 物料名称
     */
    @ComponentProperty(label = "物料名称", order = 4, path = "material.materialName")
    private String materialName;

    /**
     * 加料量
     */
    @ComponentProperty(label = "加入量", order = 5, path = "dosage")
    private BigDecimal dosage;

    /**
     * 供应商
     */
    @ComponentProperty(label = "供应商", order = 6, path = "supplier")
    private String supplier;

    /**
     * 物料批次
     */
    @ComponentProperty(label = "批次号", order = 7, path = "material.materialBatchNo")
    private String materialBatchNo;

    /**
     * 加料顺序
     */
    @ComponentProperty(label = "加料顺序", order = 8, display = false, path = "order")
    private Integer order;

}