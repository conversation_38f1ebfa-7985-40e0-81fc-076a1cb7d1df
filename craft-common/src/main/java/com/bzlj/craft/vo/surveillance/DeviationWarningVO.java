package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DeviationWarningVO {

    @ComponentProperty(label = "预警id", display = false, path = "id")
    private String id;

    /**
     * 报警时间
     */
    @ComponentProperty(label = "报警时间", order = 2, path = "alertTime", range = true, searchable = true)
    private LocalDateTime alertTime;

    /**
     * 报警类型 false:系统上报 true人工上报
     */
    @ComponentProperty(label = "报警方式", order = 3, path = "alertType")
    private String alertType;

    /**
     * 任务id
     */
    @ComponentProperty(label = "任务id", order = 4, path = "taskId", display = false)
    private String taskId;

    /**
     * 任务id
     */
    @ComponentProperty(label = "任务编号", order = 5, path = "taskCode", searchable = true)
    private String taskCode;

    @ComponentProperty(label = "所属分厂", order = 6, display = false, path = "plant.plantCode", searchable = true,excludeFromRequest = true, initialRequest = true, dataSourceType = DataSourceType.API,
            dataSource = @ComponentProperty.DataSource(
                    apiUrl = "/craft/craft/findPlantList",
                    httpMethod = HttpMethod.GET,
                    mapping = @ComponentProperty.Mapping(
                            valueKey = "plantCode",
                            labelKey = "plantName"
                    )

            )
    )
    private String plantCode;

    /**
     * 工序类型，关联字典
     */
    @ComponentProperty(label = "工序类型", order = 7, path = "processType", searchable = true, initialRequest = true, dataSourceType = DataSourceType.API, multiple = true,
            dependencies = {"plant.plantCode"}, dataSource = @ComponentProperty.DataSource(
            apiUrl = "/craft/craft/findProcessByPlantCode",
            httpMethod = HttpMethod.GET,
            apiParams = {@ComponentProperty.ApiParam(
                    key = "dependencyParams",
                    value = "{\"plantCode\":\"plant.plantCode\"}"
            )},
            mapping = @ComponentProperty.Mapping(
                    valueKey = "processName",
                    labelKey = "processName"
            )

    )
    )
    private String processType;


    /**
     * 工步名称
     */
    @ComponentProperty(label = "工步", order = 8, path = "workStepName")
    private String workStepName;

    @ComponentProperty(label = "报警内容", order = 9, path = "alarmContent")
    private String alarmContent;

    /**
     * 优化建议
     */
    @ComponentProperty(label = "优化建议", order = 10, path = "suggestion")
    private String suggestion;
}