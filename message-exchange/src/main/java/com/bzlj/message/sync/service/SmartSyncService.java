package com.bzlj.message.sync.service;

import com.bzlj.message.internaldistribution.entity.mongo.SyncStatus;
import com.bzlj.message.internaldistribution.repository.mongo.SyncStatusRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 智能同步服务
 * 自动判断执行全量同步还是增量同步
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
public class SmartSyncService {

    private final ParallelSyncService parallelSyncService;
    private final SyncStatusRepository syncStatusRepository;

    /**
     * 智能执行同步
     * 根据历史记录自动判断执行全量同步还是增量同步
     *
     * @return 同步结果
     */
    public SyncStatus performSmartSync() {
        log.info("开始智能同步判断");

        try {
            // 检查是否有成功的同步记录
            boolean hasSuccessfulSync = hasAnySuccessfulSync();
            
            if (hasSuccessfulSync) {
                log.info("检测到历史同步记录，执行增量同步");
                return parallelSyncService.performParallelIncrementalSync();
            } else {
                log.info("未检测到历史同步记录，执行全量同步");
                return parallelSyncService.performParallelFullSync();
            }
            
        } catch (Exception e) {
            log.error("智能同步执行失败", e);
            
            // 创建失败状态记录
            SyncStatus failedStatus = new SyncStatus()
                .setSyncType("SMART_SYNC")
                .setSyncStatus("FAILED")
                .setStartTime(java.time.LocalDateTime.now())
                .setEndTime(java.time.LocalDateTime.now())
                .setErrorMessage("智能同步执行异常: " + e.getMessage())
                .setTotalRecords(0L)
                .setSuccessRecords(0L)
                .setFailedRecords(0L);
            
            return syncStatusRepository.save(failedStatus);
        }
    }

    /**
     * 检查是否有任何成功的同步记录
     *
     * @return 是否有成功的同步记录
     */
    private boolean hasAnySuccessfulSync() {
        try {
            // 查找最后一次成功的全量同步
            Optional<SyncStatus> lastFullSync = syncStatusRepository.findLastSuccessfulFullSync();
            
            // 查找最后一次成功的增量同步
            Optional<SyncStatus> lastIncrementalSync = syncStatusRepository.findLastSuccessfulIncrementalSync();
            
            boolean hasSuccessfulRecord = lastFullSync.isPresent() || lastIncrementalSync.isPresent();
            
            if (hasSuccessfulRecord) {
                if (lastFullSync.isPresent()) {
                    log.debug("找到最后一次成功的全量同步: {}", lastFullSync.get().getStartTime());
                }
                if (lastIncrementalSync.isPresent()) {
                    log.debug("找到最后一次成功的增量同步: {}", lastIncrementalSync.get().getStartTime());
                }
            } else {
                log.debug("未找到任何成功的同步记录");
            }
            
            return hasSuccessfulRecord;
            
        } catch (Exception e) {
            log.error("检查同步记录失败，默认执行全量同步", e);
            return false;
        }
    }

    /**
     * 获取同步策略描述
     *
     * @return 策略描述
     */
    public String getSyncStrategy() {
        boolean hasSuccessfulSync = hasAnySuccessfulSync();
        
        if (hasSuccessfulSync) {
            return "智能策略：检测到历史同步记录，将执行增量同步";
        } else {
            return "智能策略：未检测到历史同步记录，将执行全量同步";
        }
    }

    /**
     * 获取同步历史统计
     *
     * @return 历史统计信息
     */
    public SyncHistoryStats getSyncHistoryStats() {
        try {
            // 统计各种类型的同步记录
            long totalSyncs = syncStatusRepository.count();
            
            long fullSyncCount = syncStatusRepository.findBySyncTypeOrderByStartTimeDesc("FULL_SYNC").size();
            long incrementalSyncCount = syncStatusRepository.findBySyncTypeOrderByStartTimeDesc("INCREMENTAL_SYNC").size();
            
            long successfulSyncs = syncStatusRepository.findAll().stream()
                .filter(s -> "SUCCESS".equals(s.getSyncStatus()))
                .count();
            
            long failedSyncs = syncStatusRepository.findAll().stream()
                .filter(s -> "FAILED".equals(s.getSyncStatus()))
                .count();
            
            // 最后一次同步
            Optional<SyncStatus> lastSync = syncStatusRepository.findAll().stream()
                .filter(s -> s.getEndTime() != null)
                .max((s1, s2) -> s1.getEndTime().compareTo(s2.getEndTime()));
            
            return new SyncHistoryStats(
                totalSyncs,
                fullSyncCount,
                incrementalSyncCount,
                successfulSyncs,
                failedSyncs,
                lastSync.orElse(null)
            );
            
        } catch (Exception e) {
            log.error("获取同步历史统计失败", e);
            return new SyncHistoryStats(0, 0, 0, 0, 0, null);
        }
    }

    /**
     * 检查是否有正在运行的同步任务
     *
     * @return 是否有正在运行的任务
     */
    public boolean hasRunningSyncTask() {
        return parallelSyncService.hasRunningSyncTask();
    }

    /**
     * 获取同步历史
     *
     * @param syncType 同步类型
     * @return 同步历史列表
     */
    public java.util.List<SyncStatus> getSyncHistory(String syncType) {
        return parallelSyncService.getSyncHistory(syncType);
    }

    /**
     * 同步历史统计信息
     */
    public static class SyncHistoryStats {
        private final long totalSyncs;
        private final long fullSyncCount;
        private final long incrementalSyncCount;
        private final long successfulSyncs;
        private final long failedSyncs;
        private final SyncStatus lastSync;

        public SyncHistoryStats(long totalSyncs, long fullSyncCount, long incrementalSyncCount,
                              long successfulSyncs, long failedSyncs, SyncStatus lastSync) {
            this.totalSyncs = totalSyncs;
            this.fullSyncCount = fullSyncCount;
            this.incrementalSyncCount = incrementalSyncCount;
            this.successfulSyncs = successfulSyncs;
            this.failedSyncs = failedSyncs;
            this.lastSync = lastSync;
        }

        // Getters
        public long getTotalSyncs() { return totalSyncs; }
        public long getFullSyncCount() { return fullSyncCount; }
        public long getIncrementalSyncCount() { return incrementalSyncCount; }
        public long getSuccessfulSyncs() { return successfulSyncs; }
        public long getFailedSyncs() { return failedSyncs; }
        public SyncStatus getLastSync() { return lastSync; }
    }
}
