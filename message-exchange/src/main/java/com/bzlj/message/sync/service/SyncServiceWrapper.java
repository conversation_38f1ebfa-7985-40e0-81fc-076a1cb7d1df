package com.bzlj.message.sync.service;

import com.bzlj.message.internaldistribution.entity.mongo.SyncStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 智能同步服务包装类
 * 处理事务边界和异常管理
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
public class SyncServiceWrapper {

    private final SmartSyncService smartSyncService;
    private final ParallelSyncService parallelSyncService;

    /**
     * 安全执行智能同步
     * 自动判断执行全量同步还是增量同步
     *
     * @return 同步结果
     */
    public SyncStatus safePerformSmartSync() {
        try {
            log.info("开始安全执行智能同步");
            return smartSyncService.performSmartSync();
        } catch (Exception e) {
            log.error("智能同步执行失败", e);

            // 创建失败状态记录
            SyncStatus failedStatus = new SyncStatus()
                .setSyncType("SMART_SYNC")
                .setSyncStatus("FAILED")
                .setStartTime(java.time.LocalDateTime.now())
                .setEndTime(java.time.LocalDateTime.now())
                .setErrorMessage("执行异常: " + e.getMessage())
                .setTotalRecords(0L)
                .setSuccessRecords(0L)
                .setFailedRecords(0L);

            return failedStatus;
        }
    }

    /**
     * 安全执行全量同步
     * 处理事务边界和异常
     *
     * @return 同步结果
     */
    public SyncStatus safePerformFullSync() {
        try {
            log.info("开始安全执行并行全量同步");
            return parallelSyncService.performParallelFullSync();
        } catch (Exception e) {
            log.error("并行全量同步执行失败", e);

            // 创建失败状态记录
            SyncStatus failedStatus = new SyncStatus()
                .setSyncType("FULL_SYNC")
                .setSyncStatus("FAILED")
                .setStartTime(java.time.LocalDateTime.now())
                .setEndTime(java.time.LocalDateTime.now())
                .setErrorMessage("执行异常: " + e.getMessage())
                .setTotalRecords(0L)
                .setSuccessRecords(0L)
                .setFailedRecords(0L);

            return failedStatus;
        }
    }

    /**
     * 安全执行增量同步
     * 处理事务边界和异常
     *
     * @return 同步结果
     */
    public SyncStatus safePerformIncrementalSync() {
        try {
            log.info("开始安全执行并行增量同步");
            return parallelSyncService.performParallelIncrementalSync();
        } catch (Exception e) {
            log.error("并行增量同步执行失败", e);

            // 创建失败状态记录
            SyncStatus failedStatus = new SyncStatus()
                .setSyncType("INCREMENTAL_SYNC")
                .setSyncStatus("FAILED")
                .setStartTime(java.time.LocalDateTime.now())
                .setEndTime(java.time.LocalDateTime.now())
                .setErrorMessage("执行异常: " + e.getMessage())
                .setTotalRecords(0L)
                .setSuccessRecords(0L)
                .setFailedRecords(0L);

            return failedStatus;
        }
    }

    /**
     * 检查是否有正在运行的同步任务
     *
     * @return 是否有正在运行的任务
     */
    public boolean hasRunningSyncTask() {
        try {
            return smartSyncService.hasRunningSyncTask();
        } catch (Exception e) {
            log.error("检查运行状态失败", e);
            return false;
        }
    }

    /**
     * 获取同步历史
     *
     * @param syncType 同步类型
     * @return 同步历史列表
     */
    public java.util.List<SyncStatus> getSyncHistory(String syncType) {
        try {
            return smartSyncService.getSyncHistory(syncType);
        } catch (Exception e) {
            log.error("获取同步历史失败", e);
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 获取同步策略描述
     *
     * @return 策略描述
     */
    public String getSyncStrategy() {
        try {
            return smartSyncService.getSyncStrategy();
        } catch (Exception e) {
            log.error("获取同步策略失败", e);
            return "获取策略失败";
        }
    }

    /**
     * 获取同步历史统计
     *
     * @return 历史统计信息
     */
    public SmartSyncService.SyncHistoryStats getSyncHistoryStats() {
        try {
            return smartSyncService.getSyncHistoryStats();
        } catch (Exception e) {
            log.error("获取同步历史统计失败", e);
            return new SmartSyncService.SyncHistoryStats(0, 0, 0, 0, 0, null);
        }
    }
}
