package com.bzlj.message.sync.util;

import com.bzlj.message.internaldistribution.entity.mongo.SyncStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * 同步统计验证工具
 * 用于验证同步统计信息的正确性
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
public class SyncStatsValidator {

    /**
     * 验证同步状态的统计信息
     *
     * @param syncStatus 同步状态
     * @return 验证结果
     */
    public static ValidationResult validateSyncStats(SyncStatus syncStatus) {
        if (syncStatus == null) {
            return ValidationResult.invalid("同步状态为空");
        }

        long totalRecords = syncStatus.getTotalRecords() != null ? syncStatus.getTotalRecords() : 0;
        long successRecords = syncStatus.getSuccessRecords() != null ? syncStatus.getSuccessRecords() : 0;
        long failedRecords = syncStatus.getFailedRecords() != null ? syncStatus.getFailedRecords() : 0;
        long insertedRecords = syncStatus.getInsertedRecords() != null ? syncStatus.getInsertedRecords() : 0;
        long updatedRecords = syncStatus.getUpdatedRecords() != null ? syncStatus.getUpdatedRecords() : 0;
        long skippedRecords = syncStatus.getSkippedRecords() != null ? syncStatus.getSkippedRecords() : 0;

        StringBuilder issues = new StringBuilder();

        // 验证基本统计
        long processedTotal = successRecords + failedRecords;
        if (totalRecords > 0 && processedTotal != totalRecords) {
            issues.append(String.format("总记录数不匹配: 预期=%d, 实际处理=%d; ", totalRecords, processedTotal));
        }

        // 验证增量同步的详细统计
        if ("INCREMENTAL_SYNC".equals(syncStatus.getSyncType())) {
            long detailTotal = insertedRecords + updatedRecords + skippedRecords;
            if (successRecords > 0 && detailTotal != successRecords) {
                issues.append(String.format("增量同步详细统计不匹配: 成功=%d, 详细总计=%d (新增=%d, 更新=%d, 跳过=%d); ", 
                        successRecords, detailTotal, insertedRecords, updatedRecords, skippedRecords));
            }
        }

        // 验证全量同步的统计
        if ("FULL_SYNC".equals(syncStatus.getSyncType())) {
            // 全量同步中，成功记录数应该等于新增记录数
            if (insertedRecords > 0 && successRecords != insertedRecords) {
                issues.append(String.format("全量同步统计不匹配: 成功=%d, 新增=%d; ", successRecords, insertedRecords));
            }
        }

        // 验证负数
        if (totalRecords < 0 || successRecords < 0 || failedRecords < 0 || 
            insertedRecords < 0 || updatedRecords < 0 || skippedRecords < 0) {
            issues.append("存在负数统计; ");
        }

        if (issues.length() > 0) {
            return ValidationResult.invalid(issues.toString().trim());
        }

        return ValidationResult.valid();
    }

    /**
     * 打印详细的统计信息
     *
     * @param syncStatus 同步状态
     */
    public static void printDetailedStats(SyncStatus syncStatus) {
        if (syncStatus == null) {
            log.info("同步状态为空");
            return;
        }

        log.info("=== 同步统计详情 ===");
        log.info("同步类型: {}", syncStatus.getSyncType());
        log.info("同步状态: {}", syncStatus.getSyncStatus());
        log.info("总记录数: {}", syncStatus.getTotalRecords());
        log.info("成功记录数: {}", syncStatus.getSuccessRecords());
        log.info("失败记录数: {}", syncStatus.getFailedRecords());
        
        if (syncStatus.getInsertedRecords() != null) {
            log.info("新增记录数: {}", syncStatus.getInsertedRecords());
        }
        if (syncStatus.getUpdatedRecords() != null) {
            log.info("更新记录数: {}", syncStatus.getUpdatedRecords());
        }
        if (syncStatus.getSkippedRecords() != null) {
            log.info("跳过记录数: {}", syncStatus.getSkippedRecords());
        }
        
        log.info("开始时间: {}", syncStatus.getStartTime());
        log.info("结束时间: {}", syncStatus.getEndTime());
        log.info("耗时: {} ms", syncStatus.getDurationMs());
        
        // 验证统计
        ValidationResult validation = validateSyncStats(syncStatus);
        if (validation.isValid()) {
            log.info("✅ 统计信息验证通过");
        } else {
            log.warn("❌ 统计信息验证失败: {}", validation.getMessage());
        }
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;

        private ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public static ValidationResult valid() {
            return new ValidationResult(true, "统计信息正确");
        }

        public static ValidationResult invalid(String message) {
            return new ValidationResult(false, message);
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return String.format("ValidationResult{valid=%s, message='%s'}", valid, message);
        }
    }
}
