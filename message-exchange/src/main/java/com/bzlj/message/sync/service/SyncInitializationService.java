package com.bzlj.message.sync.service;

import com.bzlj.message.sync.config.SyncScheduleConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * 同步功能初始化服务
 * 在应用启动时执行初始化逻辑
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
public class SyncInitializationService implements ApplicationRunner {

    private final SyncScheduleConfig syncScheduleConfig;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== Text2 同步功能初始化 ===");
        
        // 打印配置信息
        printConfiguration();
        
        // 检查配置合理性
        validateConfiguration();
        
        log.info("Text2 同步功能初始化完成");
    }

    /**
     * 打印配置信息
     */
    private void printConfiguration() {
        log.info("PostgreSQL 功能已启用");
        log.info("智能同步配置:");
        log.info("  - 定时同步启用: {}", syncScheduleConfig.isEnabled());

        if (syncScheduleConfig.isEnabled()) {
            log.info("  - 同步表达式: {}", syncScheduleConfig.getSyncCron());
            log.info("  - 并行同步启用: {}", syncScheduleConfig.isParallelEnabled());
            log.info("  - 页面大小: {}", syncScheduleConfig.getPageSize());
            log.info("  - 最大并发页数: {}", syncScheduleConfig.getMaxConcurrentPages());
            log.info("  - 超时时间: {} 分钟", syncScheduleConfig.getTimeoutMinutes());
            log.info("  - 允许并发: {}", syncScheduleConfig.isAllowConcurrent());
            log.info("  - 重试次数: {}", syncScheduleConfig.getRetryCount());
            log.info("  - 重试间隔: {} 秒", syncScheduleConfig.getRetryIntervalSeconds());
            log.info("  - 智能策略: 自动判断执行全量同步或增量同步");
        } else {
            log.info("  - 定时同步已禁用，可通过 API 手动触发智能同步");
        }
    }

    /**
     * 验证配置合理性
     */
    private void validateConfiguration() {
        if (syncScheduleConfig.isEnabled()) {
            // 检查 cron 表达式格式
            validateCronExpression(syncScheduleConfig.getSyncCron(), "智能同步");

            // 检查超时时间
            if (syncScheduleConfig.getTimeoutMinutes() <= 0) {
                log.warn("超时时间配置不合理: {} 分钟", syncScheduleConfig.getTimeoutMinutes());
            }

            // 检查重试配置
            if (syncScheduleConfig.getRetryCount() < 0) {
                log.warn("重试次数配置不合理: {}", syncScheduleConfig.getRetryCount());
            }

            if (syncScheduleConfig.getRetryIntervalSeconds() <= 0) {
                log.warn("重试间隔配置不合理: {} 秒", syncScheduleConfig.getRetryIntervalSeconds());
            }

            // 检查并行配置
            if (syncScheduleConfig.isParallelEnabled()) {
                if (syncScheduleConfig.getPageSize() <= 0) {
                    log.warn("页面大小配置不合理: {}", syncScheduleConfig.getPageSize());
                }

                if (syncScheduleConfig.getMaxConcurrentPages() <= 0) {
                    log.warn("最大并发页数配置不合理: {}", syncScheduleConfig.getMaxConcurrentPages());
                }

                log.info("建议: 并行同步已启用，页面大小: {}, 最大并发页数: {}",
                        syncScheduleConfig.getPageSize(), syncScheduleConfig.getMaxConcurrentPages());
            }

            if (syncScheduleConfig.isAllowConcurrent()) {
                log.warn("警告: 已允许并发执行同步任务，请确保数据库能够处理并发访问");
            }
        }
    }

    /**
     * 验证 cron 表达式格式（简单验证）
     */
    private void validateCronExpression(String cronExpression, String taskName) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            log.warn("{} cron 表达式为空", taskName);
            return;
        }
        
        String[] parts = cronExpression.trim().split("\\s+");
        if (parts.length != 6) {
            log.warn("{} cron 表达式格式可能不正确: {} (应该有6个部分)", taskName, cronExpression);
        }
    }

    /**
     * 获取启动建议
     */
    public String getStartupRecommendations() {
        StringBuilder recommendations = new StringBuilder();
        recommendations.append("=== 智能同步启动建议 ===\n");

        if (!syncScheduleConfig.isEnabled()) {
            recommendations.append("1. 定时同步已禁用，建议:\n");
            recommendations.append("   - 首次部署时执行智能同步: POST /api/sync/text2/smart-sync\n");
            recommendations.append("   - 或手动执行全量同步: POST /api/sync/text2/full-sync\n");
            recommendations.append("   - 或启用定时同步: 设置 sync.schedule.enabled=true\n\n");
        } else {
            recommendations.append("1. 智能定时同步已启用\n");
            recommendations.append("   - 系统将每分钟自动执行智能同步\n");
            recommendations.append("   - 首次运行时自动执行全量同步\n");
            recommendations.append("   - 后续自动执行增量同步\n");
            recommendations.append("   - 可通过 API 查看状态: GET /api/sync/text2/scheduler/status\n\n");
        }

        recommendations.append("2. 环境变量控制:\n");
        recommendations.append("   - POSTGRESQL_ENABLED=true (启用PostgreSQL功能)\n");
        recommendations.append("   - SYNC_SCHEDULE_ENABLED=true (启用定时同步)\n");
        recommendations.append("   - SYNC_CRON='0 * * * * ?' (同步频率)\n");
        recommendations.append("   - SYNC_PARALLEL_ENABLED=true (启用并行同步)\n");
        recommendations.append("   - SYNC_PAGE_SIZE=500 (分页大小)\n");
        recommendations.append("   - SYNC_MAX_CONCURRENT_PAGES=10 (并发页数)\n\n");

        recommendations.append("3. 监控建议:\n");
        recommendations.append("   - 查看同步策略: GET /api/sync/text2/strategy\n");
        recommendations.append("   - 定期检查同步状态: GET /api/sync/text2/status\n");
        recommendations.append("   - 监控应用日志中的智能同步信息\n");
        recommendations.append("   - 关注连续失败次数，及时处理异常\n");

        return recommendations.toString();
    }
}
