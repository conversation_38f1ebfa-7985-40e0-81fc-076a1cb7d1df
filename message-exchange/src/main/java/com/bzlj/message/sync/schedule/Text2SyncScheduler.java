package com.bzlj.message.sync.schedule;

import com.bzlj.message.internaldistribution.entity.mongo.SyncStatus;
import com.bzlj.message.sync.config.SyncScheduleConfig;
import com.bzlj.message.sync.service.SyncServiceWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Text2 智能同步定时任务调度器
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = {"postgresql.enabled", "sync.schedule.enabled"}, havingValue = "true")
public class Text2SyncScheduler {

    private final SyncServiceWrapper syncServiceWrapper;
    private final SyncScheduleConfig syncScheduleConfig;

    // 防止并发执行的标志
    private final AtomicBoolean syncRunning = new AtomicBoolean(false);

    // 统计信息
    private volatile LocalDateTime lastSyncTime;
    private volatile int consecutiveFailures = 0;

    /**
     * 智能同步定时任务
     * 自动判断执行全量同步还是增量同步
     */
    @Scheduled(cron = "${sync.schedule.sync-cron:0 * * * * ?}")
    public void performScheduledSmartSync() {
        // 检查是否允许并发执行
        if (!syncScheduleConfig.isAllowConcurrent() && syncRunning.get()) {
            log.warn("同步任务正在运行中，跳过本次执行");
            return;
        }

        // 检查是否有其他同步任务正在运行
        if (syncServiceWrapper.hasRunningSyncTask()) {
            log.warn("检测到其他同步任务正在运行，跳过本次智能同步");
            return;
        }

        if (!syncRunning.compareAndSet(false, true)) {
            log.warn("同步任务已在运行，跳过本次执行");
            return;
        }

        try {
            log.info("开始执行定时智能同步任务");

            // 获取同步策略
            String strategy = syncServiceWrapper.getSyncStrategy();
            log.info("同步策略: {}", strategy);

            lastSyncTime = LocalDateTime.now();

            SyncStatus result = executeWithRetry(() -> syncServiceWrapper.safePerformSmartSync());

            if ("SUCCESS".equals(result.getSyncStatus())) {
                consecutiveFailures = 0;

                String syncType = result.getSyncType();
                if ("FULL_SYNC".equals(syncType)) {
                    log.info("定时智能同步任务执行成功（全量同步） - 成功: {}, 失败: {}",
                            result.getSuccessRecords(), result.getFailedRecords());
                } else if ("INCREMENTAL_SYNC".equals(syncType)) {
                    log.info("定时智能同步任务执行成功（增量同步） - 成功: {}, 失败: {}, 新增: {}, 更新: {}, 跳过: {}",
                            result.getSuccessRecords(), result.getFailedRecords(),
                            result.getInsertedRecords(), result.getUpdatedRecords(), result.getSkippedRecords());
                }
            } else {
                consecutiveFailures++;
                log.error("定时智能同步任务执行失败 - 连续失败次数: {}, 错误信息: {}",
                        consecutiveFailures, result.getErrorMessage());

                // 如果连续失败次数过多，可以考虑发送告警
                if (consecutiveFailures >= 5) {
                    log.error("智能同步连续失败 {} 次，请检查系统状态", consecutiveFailures);
                    // 这里可以添加告警逻辑，比如发送邮件、钉钉消息等
                }
            }

        } catch (Exception e) {
            consecutiveFailures++;
            log.error("定时智能同步任务执行异常 - 连续失败次数: {}", consecutiveFailures, e);
        } finally {
            syncRunning.set(false);
        }
    }



    /**
     * 带重试机制的执行方法
     */
    private SyncStatus executeWithRetry(SyncOperation operation) {
        SyncStatus lastResult = null;
        int attempts = 0;
        int maxRetries = syncScheduleConfig.getRetryCount();

        while (attempts <= maxRetries) {
            try {
                lastResult = operation.execute();
                
                if ("SUCCESS".equals(lastResult.getSyncStatus())) {
                    return lastResult;
                }
                
                if (attempts < maxRetries) {
                    log.warn("同步任务执行失败，{} 秒后进行第 {} 次重试", 
                            syncScheduleConfig.getRetryIntervalSeconds(), attempts + 1);
                    Thread.sleep(syncScheduleConfig.getRetryIntervalSeconds() * 1000L);
                }
                
            } catch (Exception e) {
                log.error("同步任务执行异常，尝试次数: {}", attempts + 1, e);
                
                if (attempts < maxRetries) {
                    try {
                        Thread.sleep(syncScheduleConfig.getRetryIntervalSeconds() * 1000L);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
            
            attempts++;
        }

        return lastResult;
    }

    /**
     * 获取定时任务状态信息
     */
    public String getSchedulerStatus() {
        StringBuilder status = new StringBuilder();
        status.append("=== 智能同步定时任务状态 ===\n");
        status.append("配置状态:\n");
        status.append("  - 定时同步启用: ").append(syncScheduleConfig.isEnabled()).append("\n");
        status.append("  - 同步表达式: ").append(syncScheduleConfig.getSyncCron()).append("\n");
        status.append("  - 允许并发: ").append(syncScheduleConfig.isAllowConcurrent()).append("\n");
        status.append("  - 并行启用: ").append(syncScheduleConfig.isParallelEnabled()).append("\n");
        status.append("  - 页面大小: ").append(syncScheduleConfig.getPageSize()).append("\n");
        status.append("  - 最大并发页数: ").append(syncScheduleConfig.getMaxConcurrentPages()).append("\n");

        status.append("\n运行状态:\n");
        status.append("  - 同步任务运行中: ").append(syncRunning.get()).append("\n");
        status.append("  - 连续失败次数: ").append(consecutiveFailures).append("\n");
        status.append("  - 上次同步时间: ").append(lastSyncTime != null ? lastSyncTime : "未执行").append("\n");

        // 添加同步策略信息
        try {
            String strategy = syncServiceWrapper.getSyncStrategy();
            status.append("  - 当前策略: ").append(strategy).append("\n");
        } catch (Exception e) {
            status.append("  - 当前策略: 获取失败\n");
        }

        return status.toString();
    }

    /**
     * 同步操作函数式接口
     */
    @FunctionalInterface
    private interface SyncOperation {
        SyncStatus execute() throws Exception;
    }
}
