package com.bzlj.message.sync.converter;

import cn.hutool.core.util.StrUtil;
import com.bzlj.message.internaldistribution.entity.mongo.MessageRules;
import com.bzlj.message.jpa.entity.Text2Entity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Text2 到 MessageRules 的数据转换器
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Component
public class Text2ToMessageRulesConverter {

    /**
     * 将 Text2Entity 转换为 MessageRules
     *
     * @param text2Entity Text2 实体
     * @return MessageRules 实体
     */
    public MessageRules convert(Text2Entity text2Entity) {
        if (text2Entity == null) {
            return null;
        }

        try {
            MessageRules messageRules = new MessageRules();

            // 基本字段映射
            messageRules.setTelegraphTextCode(text2Entity.getTcNo())  // TC编号 -> 电文号
                       .setField(text2Entity.getTcItemName())         // TC项目名称 -> 字段名
                       .setFieldName(text2Entity.getTcItemName())     // TC项目名称 -> 字段中文名
                       .setFieldType(convertFieldType(text2Entity.getTcItemType()))  // TC项目类型 -> 字段类型
                       .setFieldSize(text2Entity.getTcItemLen())      // TC项目长度 -> 字段长度
                       .setPrecision(text2Entity.getTcItemDec())      // TC项目小数位数 -> 精度
                       .setSorts(text2Entity.getTcItemSeqNo())        // TC项目序号 -> 排序号
                       .setDefaultValue(text2Entity.getTcItemDefaultValue())  // TC项目默认值 -> 缺省值
                       .setRemark(text2Entity.getRemark());           // 备注 -> 备注

            // 业务逻辑转换
            messageRules.setIsRequired(convertToRequired(text2Entity.getKeyFlag()))  // 关键字标志 -> 是否必须
                       .setIsLoop(convertToLoop(text2Entity.getSegmentLoopFlag(), text2Entity.getLoopCount()));  // 段循环标志+循环次数 -> 是否循环体

            // 设置其他字段的默认值
            messageRules.setUnit("")  // 单位字段在 Text2 中没有对应，设为空
                       .setMax("")    // 上限字段在 Text2 中没有对应，设为空
                       .setMin("");   // 下限字段在 Text2 中没有对应，设为空

            log.debug("转换成功: TC编号={}, 项目名称={}", text2Entity.getTcNo(), text2Entity.getTcItemName());
            return messageRules;

        } catch (Exception e) {
            log.error("转换失败: TC编号={}, 项目名称={}, 错误信息={}", 
                     text2Entity.getTcNo(), text2Entity.getTcItemName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换字段类型
     * Text2 的 tc_item_type 转换为 MessageRules 的 field_type
     *
     * @param tcItemType TC项目类型
     * @return 字段类型 (C字符串 N数值)
     */
    private String convertFieldType(String tcItemType) {
        if (StrUtil.isBlank(tcItemType)) {
            return "C";  // 默认为字符串类型
        }

        String type = tcItemType.trim().toUpperCase();
        
        // 根据业务规则进行映射
        switch (type) {
            case "CHAR":
            case "VARCHAR":
            case "STRING":
            case "TEXT":
                return "C";  // 字符串类型
            case "INT":
            case "INTEGER":
            case "DECIMAL":
            case "NUMERIC":
            case "FLOAT":
            case "DOUBLE":
            case "NUMBER":
                return "N";  // 数值类型
            default:
                // 如果包含数字相关关键字，判断为数值类型
                if (type.contains("NUM") || type.contains("INT") || type.contains("DEC")) {
                    return "N";
                }
                return "C";  // 默认为字符串类型
        }
    }

    /**
     * 转换是否必须字段
     * Text2 的 key_flag 转换为 MessageRules 的 is_required
     *
     * @param keyFlag 关键字标志
     * @return 是否必须 (0否 1是)
     */
    private Integer convertToRequired(String keyFlag) {
        if (StrUtil.isBlank(keyFlag)) {
            return 0;  // 默认非必须
        }

        String flag = keyFlag.trim().toUpperCase();
        
        // 根据业务规则判断
        if ("Y".equals(flag) || "YES".equals(flag) || "1".equals(flag) || "TRUE".equals(flag)) {
            return 1;  // 必须
        }
        
        return 0;  // 非必须
    }

    /**
     * 转换是否循环体字段
     * Text2 的 segment_loop_flag 和 loop_count 转换为 MessageRules 的 is_loop
     *
     * @param segmentLoopFlag 段循环标志
     * @param loopCount       循环次数
     * @return 是否循环体 (0否 1是)
     */
    private Integer convertToLoop(String segmentLoopFlag, Integer loopCount) {
        // 如果循环次数大于1，认为是循环体
        if (loopCount != null && loopCount > 1) {
            return 1;
        }

        // 如果段循环标志表示循环
        if (StrUtil.isNotBlank(segmentLoopFlag)) {
            String flag = segmentLoopFlag.trim().toUpperCase();
            if ("Y".equals(flag) || "YES".equals(flag) || "1".equals(flag) || "TRUE".equals(flag)) {
                return 1;
            }
        }

        return 0;  // 非循环体
    }

    /**
     * 智能更新已存在的 MessageRules
     * 只更新非空且有差异的字段
     *
     * @param existing    已存在的 MessageRules
     * @param text2Entity 新的 Text2Entity 数据
     * @return 是否有字段被更新
     */
    public boolean updateExistingMessageRules(MessageRules existing, Text2Entity text2Entity) {
        if (existing == null || text2Entity == null) {
            return false;
        }

        boolean hasUpdated = false;

        try {
            // 转换新数据
            MessageRules newData = convert(text2Entity);
            if (newData == null) {
                return false;
            }

            // 只更新非空且有差异的字段
            hasUpdated |= updateFieldIfDifferent(existing::setFieldName, existing.getFieldName(), newData.getFieldName(), "fieldName");
            hasUpdated |= updateFieldIfDifferent(existing::setFieldType, existing.getFieldType(), newData.getFieldType(), "fieldType");
            hasUpdated |= updateFieldIfDifferent(existing::setFieldSize, existing.getFieldSize(), newData.getFieldSize(), "fieldSize");
            hasUpdated |= updateFieldIfDifferent(existing::setPrecision, existing.getPrecision(), newData.getPrecision(), "precision");
            hasUpdated |= updateFieldIfDifferent(existing::setIsRequired, existing.getIsRequired(), newData.getIsRequired(), "isRequired");
            hasUpdated |= updateFieldIfDifferent(existing::setIsLoop, existing.getIsLoop(), newData.getIsLoop(), "isLoop");
            hasUpdated |= updateFieldIfDifferent(existing::setSorts, existing.getSorts(), newData.getSorts(), "sorts");
            hasUpdated |= updateFieldIfDifferent(existing::setDefaultValue, existing.getDefaultValue(), newData.getDefaultValue(), "defaultValue");
            hasUpdated |= updateFieldIfDifferent(existing::setRemark, existing.getRemark(), newData.getRemark(), "remark");

            if (hasUpdated) {
                log.debug("更新记录: TC编号={}, 字段名={}", text2Entity.getTcNo(), text2Entity.getTcItemName());
            }

        } catch (Exception e) {
            log.error("智能更新失败: TC编号={}, 项目名称={}, 错误信息={}",
                     text2Entity.getTcNo(), text2Entity.getTcItemName(), e.getMessage(), e);
        }

        return hasUpdated;
    }

    /**
     * 更新字段（如果新值非空且与旧值不同）
     *
     * @param setter    字段设置方法
     * @param oldValue  旧值
     * @param newValue  新值
     * @param fieldName 字段名（用于日志）
     * @return 是否更新了字段
     */
    private <T> boolean updateFieldIfDifferent(java.util.function.Function<T, MessageRules> setter, T oldValue, T newValue, String fieldName) {
        // 如果新值为空，不更新
        if (newValue == null) {
            return false;
        }

        // 对于字符串类型，检查是否为空白
        if (newValue instanceof String && StrUtil.isBlank((String) newValue)) {
            return false;
        }

        // 如果新值与旧值相同，不更新
        if (java.util.Objects.equals(oldValue, newValue)) {
            return false;
        }

        // 执行更新
        setter.apply(newValue);
        log.debug("字段 {} 已更新: {} -> {}", fieldName, oldValue, newValue);
        return true;
    }

    /**
     * 比较两个 MessageRules 对象的差异
     *
     * @param existing 已存在的记录
     * @param newData  新数据
     * @return 差异字段列表
     */
    public java.util.List<String> getDifferences(MessageRules existing, MessageRules newData) {
        java.util.List<String> differences = new java.util.ArrayList<>();

        if (existing == null || newData == null) {
            return differences;
        }

        if (!java.util.Objects.equals(existing.getFieldName(), newData.getFieldName())) {
            differences.add("fieldName: " + existing.getFieldName() + " -> " + newData.getFieldName());
        }
        if (!java.util.Objects.equals(existing.getFieldType(), newData.getFieldType())) {
            differences.add("fieldType: " + existing.getFieldType() + " -> " + newData.getFieldType());
        }
        if (!java.util.Objects.equals(existing.getFieldSize(), newData.getFieldSize())) {
            differences.add("fieldSize: " + existing.getFieldSize() + " -> " + newData.getFieldSize());
        }
        if (!java.util.Objects.equals(existing.getPrecision(), newData.getPrecision())) {
            differences.add("precision: " + existing.getPrecision() + " -> " + newData.getPrecision());
        }
        if (!java.util.Objects.equals(existing.getIsRequired(), newData.getIsRequired())) {
            differences.add("isRequired: " + existing.getIsRequired() + " -> " + newData.getIsRequired());
        }
        if (!java.util.Objects.equals(existing.getIsLoop(), newData.getIsLoop())) {
            differences.add("isLoop: " + existing.getIsLoop() + " -> " + newData.getIsLoop());
        }
        if (!java.util.Objects.equals(existing.getSorts(), newData.getSorts())) {
            differences.add("sorts: " + existing.getSorts() + " -> " + newData.getSorts());
        }
        if (!java.util.Objects.equals(existing.getDefaultValue(), newData.getDefaultValue())) {
            differences.add("defaultValue: " + existing.getDefaultValue() + " -> " + newData.getDefaultValue());
        }
        if (!java.util.Objects.equals(existing.getRemark(), newData.getRemark())) {
            differences.add("remark: " + existing.getRemark() + " -> " + newData.getRemark());
        }

        return differences;
    }

    /**
     * 生成 MessageRules 的唯一标识
     * 基于电文号和字段名生成
     *
     * @param telegraphTextCode 电文号
     * @param field            字段名
     * @return 唯一标识
     */
    public String generateMessageRulesId(String telegraphTextCode, String field) {
        return telegraphTextCode + "_" + field;
    }
}
