package com.bzlj.message.sync.service;

import cn.hutool.core.collection.CollUtil;
import com.bzlj.message.common.util.CompletableFutureUtil;
import com.bzlj.message.internaldistribution.entity.mongo.MessageRules;
import com.bzlj.message.internaldistribution.entity.mongo.SyncStatus;
import com.bzlj.message.internaldistribution.repository.mongo.MessageRulesRepository;
import com.bzlj.message.internaldistribution.repository.mongo.SyncStatusRepository;
import com.bzlj.message.jpa.entity.Text2Entity;
import com.bzlj.message.jpa.repository.Text2Repository;
import com.bzlj.message.sync.config.SyncScheduleConfig;
import com.bzlj.message.sync.converter.Text2ToMessageRulesConverter;
import com.bzlj.message.sync.util.SyncStatsValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 并行同步服务
 * 使用虚拟线程和分页处理提高同步效率
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
public class ParallelSyncService {

    private final Text2Repository text2Repository;
    private final MessageRulesRepository messageRulesRepository;
    private final SyncStatusRepository syncStatusRepository;
    private final Text2ToMessageRulesConverter converter;
    private final SyncScheduleConfig syncScheduleConfig;

    /**
     * 并行执行全量同步
     *
     * @return 同步结果
     */
    public SyncStatus performParallelFullSync() {
        log.info("开始并行全量同步");
        
        SyncStatus syncStatus = createSyncStatus("FULL_SYNC");
        
        try {
            // 1. 清空现有数据
            log.info("清空现有 MessageRules 数据");
            messageRulesRepository.deleteAll();

            // 2. 获取总记录数（已过滤无效记录）
            long totalRecords = text2Repository.countAll();
            syncStatus.setTotalRecords(totalRecords);
            
            if (totalRecords == 0) {
                return completeSyncStatus(syncStatus, 0, 0, 0, 0, 0, "没有数据需要同步");
            }
            
            // 3. 计算总页数
            int pageSize = syncScheduleConfig.getPageSize();
            int maxConcurrentPages = syncScheduleConfig.getMaxConcurrentPages();
            int totalPages = (int) Math.ceil((double) totalRecords / pageSize);
            log.info("总记录数: {}, 分页大小: {}, 总页数: {}, 最大并发页数: {}",
                    totalRecords, pageSize, totalPages, maxConcurrentPages);
            
            // 4. 并行处理各页
            AtomicLong successCount = new AtomicLong(0);
            AtomicLong failedCount = new AtomicLong(0);
            AtomicLong insertedCount = new AtomicLong(0);
            
            List<CompletableFuture<PageSyncResult>> futures = new ArrayList<>();
            
            // 分批提交任务，避免创建过多线程
            for (int startPage = 0; startPage < totalPages; startPage += maxConcurrentPages) {
                int endPage = Math.min(startPage + maxConcurrentPages, totalPages);
                
                // 提交当前批次的页面处理任务
                for (int pageNum = startPage; pageNum < endPage; pageNum++) {
                    final int currentPage = pageNum;

                    CompletableFuture<PageSyncResult> future = CompletableFutureUtil
                            .runAsync(() -> processPageForFullSync(currentPage, pageSize));
                    
                    futures.add(future);
                }
                
                // 等待当前批次完成
                CompletableFutureUtil.allOf(futures.toArray(new CompletableFuture[0])).join();
                
                // 收集结果
                for (CompletableFuture<PageSyncResult> future : futures) {
                    try {
                        PageSyncResult result = future.get();
                        successCount.addAndGet(result.getSuccessCount());
                        failedCount.addAndGet(result.getFailedCount());
                        insertedCount.addAndGet(result.getInsertedCount());
                    } catch (Exception e) {
                        log.error("获取页面处理结果失败", e);
                        // 这里不应该直接添加 pageSize，因为我们不知道实际处理了多少条记录
                        // 页面处理方法内部已经处理了异常情况
                    }
                }
                
                futures.clear();
                log.info("已完成 {}/{} 批次，当前成功: {}, 失败: {}",
                        (startPage / maxConcurrentPages) + 1,
                        (totalPages + maxConcurrentPages - 1) / maxConcurrentPages,
                        successCount.get(), failedCount.get());
            }
            
            SyncStatus result = completeSyncStatus(syncStatus, successCount.get(), failedCount.get(),
                    insertedCount.get(), 0, 0, "并行全量同步完成");

            // 验证统计信息
            SyncStatsValidator.ValidationResult validation = SyncStatsValidator.validateSyncStats(result);
            if (!validation.isValid()) {
                log.warn("全量同步统计信息验证失败: {}", validation.getMessage());
            }

            return result;
            
        } catch (Exception e) {
            log.error("并行全量同步失败", e);
            return failSyncStatus(syncStatus, e.getMessage());
        }
    }

    /**
     * 并行执行增量同步
     *
     * @return 同步结果
     */
    public SyncStatus performParallelIncrementalSync() {
        log.info("开始并行增量同步");
        
        SyncStatus syncStatus = createSyncStatus("INCREMENTAL_SYNC");
        
        try {
            // 1. 获取上次同步时间
            String lastSyncTime = getLastSyncTime();
            log.info("上次同步时间: {}", lastSyncTime);

            // 2. 获取需要同步的记录总数（已过滤无效记录）
            long totalRecords;
            if (lastSyncTime != null) {
                // 使用专门的统计方法，统计创建时间或更新时间在lastSyncTime之后的记录
                totalRecords = text2Repository.countByCreateTimeOrReviseTimeAfter(lastSyncTime);
            } else {
                totalRecords = text2Repository.countAll();
            }
            
            syncStatus.setTotalRecords(totalRecords);
            
            if (totalRecords == 0) {
                return completeSyncStatus(syncStatus, 0, 0, 0, 0, 0, "没有需要同步的数据");
            }
            
            // 3. 计算总页数
            int pageSize = syncScheduleConfig.getPageSize();
            int maxConcurrentPages = syncScheduleConfig.getMaxConcurrentPages();
            int totalPages = (int) Math.ceil((double) totalRecords / pageSize);
            log.info("需要同步记录数: {}, 分页大小: {}, 总页数: {}, 最大并发页数: {}",
                    totalRecords, pageSize, totalPages, maxConcurrentPages);
            
            // 4. 并行处理各页
            AtomicLong successCount = new AtomicLong(0);
            AtomicLong failedCount = new AtomicLong(0);
            AtomicLong insertedCount = new AtomicLong(0);
            AtomicLong updatedCount = new AtomicLong(0);
            AtomicLong skippedCount = new AtomicLong(0);
            
            List<CompletableFuture<PageSyncResult>> futures = new ArrayList<>();
            
            // 分批处理
            for (int startPage = 0; startPage < totalPages; startPage += maxConcurrentPages) {
                int endPage = Math.min(startPage + maxConcurrentPages, totalPages);
                
                for (int pageNum = startPage; pageNum < endPage; pageNum++) {
                    final int currentPage = pageNum;
                    final String syncTime = lastSyncTime;
                    
                    CompletableFuture<PageSyncResult> future = CompletableFutureUtil.runAsync(() -> {
                        return processPageForIncrementalSync(currentPage, syncTime, pageSize);
                    });
                    
                    futures.add(future);
                }
                
                // 等待当前批次完成
                CompletableFutureUtil.allOf(futures.toArray(new CompletableFuture[0])).join();
                
                // 收集结果
                for (CompletableFuture<PageSyncResult> future : futures) {
                    try {
                        PageSyncResult result = future.get();
                        successCount.addAndGet(result.getSuccessCount());
                        failedCount.addAndGet(result.getFailedCount());
                        insertedCount.addAndGet(result.getInsertedCount());
                        updatedCount.addAndGet(result.getUpdatedCount());
                        skippedCount.addAndGet(result.getSkippedCount());
                    } catch (Exception e) {
                        log.error("获取页面处理结果失败", e);
                        // 这里不应该直接添加 pageSize，因为我们不知道实际处理了多少条记录
                        // 页面处理方法内部已经处理了异常情况
                    }
                }
                
                futures.clear();
                log.info("已完成 {}/{} 批次，当前成功: {}, 失败: {}, 新增: {}, 更新: {}, 跳过: {}",
                        (startPage / maxConcurrentPages) + 1,
                        (totalPages + maxConcurrentPages - 1) / maxConcurrentPages,
                        successCount.get(), failedCount.get(), insertedCount.get(),
                        updatedCount.get(), skippedCount.get());
            }
            
            SyncStatus result = completeSyncStatus(syncStatus, successCount.get(), failedCount.get(),
                    insertedCount.get(), updatedCount.get(), skippedCount.get(), "并行增量同步完成");

            // 验证统计信息
            SyncStatsValidator.ValidationResult validation = SyncStatsValidator.validateSyncStats(result);
            if (!validation.isValid()) {
                log.warn("增量同步统计信息验证失败: {}", validation.getMessage());
            }

            return result;
            
        } catch (Exception e) {
            log.error("并行增量同步失败", e);
            return failSyncStatus(syncStatus, e.getMessage());
        }
    }

    /**
     * 处理单页数据（全量同步）
     */
    private PageSyncResult processPageForFullSync(int pageNum, int pageSize) {
        try {
            log.debug("开始处理第 {} 页", pageNum + 1);
            
            Pageable pageable = PageRequest.of(pageNum, pageSize);
            Page<Text2Entity> page = text2Repository.findAllOrderByCreateTimeAndSeq(pageable);
            
            List<Text2Entity> entities = page.getContent();
            if (CollUtil.isEmpty(entities)) {
                return new PageSyncResult(0, 0, 0, 0, 0);
            }
            
            List<MessageRules> messageRulesList = new ArrayList<>();
            int successCount = 0;
            int failedCount = 0;
            
            for (Text2Entity entity : entities) {
                try {
                    MessageRules messageRules = converter.convert(entity);
                    if (messageRules != null) {
                        messageRulesList.add(messageRules);
                        successCount++;
                    } else {
                        failedCount++;
                    }
                } catch (Exception e) {
                    log.error("转换实体失败: TC编号={}, 项目名称={}",
                            entity.getTcNo(), entity.getTcItemName(), e);
                    failedCount++;
                }
            }
            
            // 批量保存
            if (CollUtil.isNotEmpty(messageRulesList)) {
                try {
                    messageRulesRepository.saveAll(messageRulesList);
                    log.debug("第 {} 页保存成功，记录数: {}", pageNum + 1, messageRulesList.size());
                } catch (Exception e) {
                    log.error("第 {} 页批量保存失败，尝试逐条保存", pageNum + 1, e);
                    // 逐条保存
                    int savedCount = 0;
                    for (MessageRules rules : messageRulesList) {
                        try {
                            messageRulesRepository.save(rules);
                            savedCount++;
                        } catch (Exception ex) {
                            log.error("保存单条记录失败: TC编号={}, 字段={}", 
                                    rules.getTelegraphTextCode(), rules.getField(), ex);
                        }
                    }
                    successCount = savedCount;
                    failedCount = messageRulesList.size() - savedCount;
                }
            }
            
            PageSyncResult result = new PageSyncResult(successCount, failedCount, successCount, 0, 0);
            log.debug("第 {} 页处理完成 - 总记录: {}, 成功: {}, 失败: {}",
                    pageNum + 1, entities.size(), successCount, failedCount);
            return result;

        } catch (Exception e) {
            log.error("处理第 {} 页失败", pageNum + 1, e);
            // 返回实际的页面大小，而不是配置的页面大小
            Pageable pageable = PageRequest.of(pageNum, pageSize);
            try {
                Page<Text2Entity> page = text2Repository.findAllOrderByCreateTimeAndSeq(pageable);
                int actualSize = page.getContent().size();
                return new PageSyncResult(0, actualSize, 0, 0, 0);
            } catch (Exception ex) {
                log.error("获取页面大小失败", ex);
                return new PageSyncResult(0, 0, 0, 0, 0);
            }
        }
    }

    /**
     * 处理单页数据（增量同步）
     */
    private PageSyncResult processPageForIncrementalSync(int pageNum, String lastSyncTime, int pageSize) {
        try {
            log.debug("开始处理增量同步第 {} 页", pageNum + 1);

            Pageable pageable = PageRequest.of(pageNum, pageSize);
            Page<Text2Entity> page;

            if (lastSyncTime != null) {
                // 查询创建时间或更新时间在lastSyncTime之后的记录
                page = text2Repository.findByCreateTimeOrReviseTimeAfter(lastSyncTime, pageable);
            } else {
                page = text2Repository.findAllOrderByCreateTimeAndSeq(pageable);
            }

            List<Text2Entity> entities = page.getContent();
            if (CollUtil.isEmpty(entities)) {
                return new PageSyncResult(0, 0, 0, 0, 0);
            }

            List<MessageRules> toSaveList = new ArrayList<>();
            int successCount = 0;
            int failedCount = 0;
            int insertedCount = 0;
            int updatedCount = 0;
            int skippedCount = 0;

            for (Text2Entity entity : entities) {
                try {
                    MessageRules newRules = converter.convert(entity);
                    if (newRules != null) {
                        // 检查是否已存在
                        var existingOpt = messageRulesRepository
                            .findByTelegraphTextCodeAndField(newRules.getTelegraphTextCode(), newRules.getField());

                        if (existingOpt.isPresent()) {
                            // 智能更新
                            MessageRules existing = existingOpt.get();
                            boolean hasUpdated = converter.updateExistingMessageRules(existing, entity);

                            if (hasUpdated) {
                                toSaveList.add(existing);
                                updatedCount++;
                            } else {
                                skippedCount++;
                            }
                        } else {
                            // 新增
                            toSaveList.add(newRules);
                            insertedCount++;
                        }

                        successCount++;
                    } else {
                        failedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理实体失败: TC编号={}, 项目名称={}",
                            entity.getTcNo(), entity.getTcItemName(), e);
                    failedCount++;
                }
            }

            // 批量保存需要更新的数据
            if (CollUtil.isNotEmpty(toSaveList)) {
                try {
                    messageRulesRepository.saveAll(toSaveList);
                    log.debug("第 {} 页保存成功，记录数: {}", pageNum + 1, toSaveList.size());
                } catch (Exception e) {
                    log.error("第 {} 页批量保存失败，尝试逐条保存", pageNum + 1, e);
                    // 逐条保存
                    for (MessageRules rules : toSaveList) {
                        try {
                            messageRulesRepository.save(rules);
                        } catch (Exception ex) {
                            log.error("保存单条记录失败: TC编号={}, 字段={}",
                                    rules.getTelegraphTextCode(), rules.getField(), ex);
                            failedCount++;
                            successCount--;
                        }
                    }
                }
            }

            PageSyncResult result = new PageSyncResult(successCount, failedCount, insertedCount, updatedCount, skippedCount);
            log.debug("第 {} 页增量同步完成 - 总记录: {}, 成功: {}, 失败: {}, 新增: {}, 更新: {}, 跳过: {}",
                    pageNum + 1, entities.size(), successCount, failedCount, insertedCount, updatedCount, skippedCount);
            return result;

        } catch (Exception e) {
            log.error("处理增量同步第 {} 页失败", pageNum + 1, e);
            // 返回实际的页面大小，而不是配置的页面大小
            Pageable pageable = PageRequest.of(pageNum, pageSize);
            try {
                Page<Text2Entity> page;
                if (lastSyncTime != null) {
                    // 查询创建时间或更新时间在lastSyncTime之后的记录
                    page = text2Repository.findByCreateTimeOrReviseTimeAfter(lastSyncTime, pageable);
                } else {
                    page = text2Repository.findAllOrderByCreateTimeAndSeq(pageable);
                }
                int actualSize = page.getContent().size();
                return new PageSyncResult(0, actualSize, 0, 0, 0);
            } catch (Exception ex) {
                log.error("获取页面大小失败", ex);
                return new PageSyncResult(0, 0, 0, 0, 0);
            }
        }
    }

    /**
     * 创建同步状态记录
     */
    private SyncStatus createSyncStatus(String syncType) {
        LocalDateTime now = LocalDateTime.now();
        SyncStatus syncStatus = new SyncStatus()
            .setSyncType(syncType)
            .setSyncStatus("RUNNING")
            .setStartTime(now)
            .setCreateTime(now)
            .setUpdateTime(now);

        return syncStatusRepository.save(syncStatus);
    }

    /**
     * 完成同步状态
     */
    private SyncStatus completeSyncStatus(SyncStatus syncStatus, long successCount, long failedCount,
                                        long insertedCount, long updatedCount, long skippedCount, String message) {
        LocalDateTime endTime = LocalDateTime.now();
        long duration = java.time.Duration.between(syncStatus.getStartTime(), endTime).toMillis();

        // 设置最后同步的创建时间，用于下次增量同步的基准时间
        String lastSyncCreateTime = getCurrentTimeString();

        syncStatus.setSuccessRecords(successCount)
                 .setFailedRecords(failedCount)
                 .setInsertedRecords(insertedCount)
                 .setUpdatedRecords(updatedCount)
                 .setSkippedRecords(skippedCount)
                 .setSyncStatus("SUCCESS")
                 .setEndTime(endTime)
                 .setDurationMs(duration)
                 .setUpdateTime(endTime)
                 .setLastSyncCreateTime(lastSyncCreateTime)
                 .setRemark(message + String.format(" - 成功: %d, 失败: %d, 新增: %d, 更新: %d, 跳过: %d",
                         successCount, failedCount, insertedCount, updatedCount, skippedCount));

        return syncStatusRepository.save(syncStatus);
    }

    /**
     * 失败同步状态
     */
    private SyncStatus failSyncStatus(SyncStatus syncStatus, String errorMessage) {
        LocalDateTime endTime = LocalDateTime.now();
        long duration = java.time.Duration.between(syncStatus.getStartTime(), endTime).toMillis();

        syncStatus.setSyncStatus("FAILED")
                 .setEndTime(endTime)
                 .setDurationMs(duration)
                 .setUpdateTime(endTime)
                 .setErrorMessage(errorMessage);

        return syncStatusRepository.save(syncStatus);
    }

    /**
     * 获取上次同步时间
     * 获取最后一次成功的同步记录（不区分增量或全量），获取到最后同步时间
     * 用于增量同步时确定同步基准时间，只同步在此时间之后创建或更新的记录
     */
    private String getLastSyncTime() {
        // 直接获取最后一次成功的同步记录（不区分类型，按 startTime 倒序排列的第一条）
        Optional<SyncStatus> lastSyncOpt = syncStatusRepository.findLastSuccessfulSync();

        if (lastSyncOpt.isPresent()) {
            SyncStatus lastSync = lastSyncOpt.get();

            // 返回 lastSyncCreateTime，如果为空则返回 startTime 的字符串形式
            String lastSyncCreateTime = lastSync.getLastSyncCreateTime();
            if (lastSyncCreateTime != null && !lastSyncCreateTime.trim().isEmpty()) {
                return lastSyncCreateTime;
            } else {
                // 如果 lastSyncCreateTime 为空，使用 startTime
                return lastSync.getStartTime().toString();
            }
        }

        return null;
    }

    /**
     * 获取当前时间字符串
     */
    private String getCurrentTimeString() {
        return LocalDateTime.now().toString();
    }

    /**
     * 检查是否有正在运行的同步任务
     *
     * @return 是否有正在运行的任务
     */
    public boolean hasRunningSyncTask() {
        return syncStatusRepository.countRunningSyncTasks() > 0;
    }

    /**
     * 获取同步状态历史
     *
     * @param syncType 同步类型，null 表示所有类型
     * @return 同步状态列表
     */
    public List<SyncStatus> getSyncHistory(String syncType) {
        if (cn.hutool.core.util.StrUtil.isBlank(syncType)) {
            return syncStatusRepository.findAll();
        }
        return syncStatusRepository.findBySyncTypeOrderByStartTimeDesc(syncType);
    }



    /**
     * 页面同步结果
     */
    public static class PageSyncResult {
        private final long successCount;
        private final long failedCount;
        private final long insertedCount;
        private final long updatedCount;
        private final long skippedCount;

        public PageSyncResult(long successCount, long failedCount, long insertedCount,
                            long updatedCount, long skippedCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.insertedCount = insertedCount;
            this.updatedCount = updatedCount;
            this.skippedCount = skippedCount;
        }

        public long getSuccessCount() { return successCount; }
        public long getFailedCount() { return failedCount; }
        public long getInsertedCount() { return insertedCount; }
        public long getUpdatedCount() { return updatedCount; }
        public long getSkippedCount() { return skippedCount; }
    }
}
