package com.bzlj.message.sync.controller;

import com.bzlj.message.internaldistribution.entity.mongo.SyncStatus;
import com.bzlj.message.sync.config.SyncScheduleConfig;
import com.bzlj.message.sync.schedule.Text2SyncScheduler;
import com.bzlj.message.sync.service.SyncServiceWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Text2 到 MessageRules 智能同步控制器
 * 提供手动触发智能同步和查询同步状态的接口
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@RestController
@RequestMapping("/api/sync/text2")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
@Tag(name = "Text2智能同步管理", description = "Text2到MessageRules的智能数据同步管理接口")
public class Text2SyncController {
    private final SyncServiceWrapper syncServiceWrapper;

    @Autowired(required = false)
    private Text2SyncScheduler text2SyncScheduler;

    @Autowired(required = false)
    private SyncScheduleConfig syncScheduleConfig;

    /**
     * 执行智能同步
     */
    @PostMapping("/smart-sync")
    @Operation(summary = "执行智能同步", description = "自动判断执行全量同步还是增量同步")
    public ResponseEntity<Map<String, Object>> performSmartSync() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查是否有正在运行的任务
            if (syncServiceWrapper.hasRunningSyncTask()) {
                result.put("success", false);
                result.put("message", "已有同步任务正在运行，请稍后再试");
                return ResponseEntity.badRequest().body(result);
            }

            // 获取同步策略
            String strategy = syncServiceWrapper.getSyncStrategy();
            log.info("开始执行智能同步，策略: {}", strategy);

            // 异步执行同步任务
            new Thread(() -> {
                try {
                    syncServiceWrapper.safePerformSmartSync();
                } catch (Exception e) {
                    log.error("智能同步执行失败", e);
                }
            }).start();

            result.put("success", true);
            result.put("strategy", strategy);
            result.put("message", "智能同步任务已启动，请通过状态查询接口查看进度");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("启动智能同步失败", e);
            result.put("success", false);
            result.put("message", "启动智能同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 执行全量同步
     */
    @PostMapping("/full-sync")
    @Operation(summary = "执行全量同步", description = "清空MessageRules表并使用虚拟线程并行同步所有Text2数据")
    public ResponseEntity<Map<String, Object>> performFullSync() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否有正在运行的任务
            if (syncServiceWrapper.hasRunningSyncTask()) {
                result.put("success", false);
                result.put("message", "已有同步任务正在运行，请稍后再试");
                return ResponseEntity.badRequest().body(result);
            }

            log.info("开始执行并行全量同步");

            // 异步执行同步任务
            new Thread(() -> {
                try {
                    syncServiceWrapper.safePerformFullSync();
                } catch (Exception e) {
                    log.error("全量同步执行失败", e);
                }
            }).start();

            result.put("success", true);
            result.put("message", "并行全量同步任务已启动，请通过状态查询接口查看进度");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("启动全量同步失败", e);
            result.put("success", false);
            result.put("message", "启动全量同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 执行增量同步
     */
    @PostMapping("/incremental-sync")
    @Operation(summary = "执行增量同步", description = "基于创建时间使用虚拟线程并行同步新增或修改的Text2数据")
    public ResponseEntity<Map<String, Object>> performIncrementalSync() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否有正在运行的任务
            if (syncServiceWrapper.hasRunningSyncTask()) {
                result.put("success", false);
                result.put("message", "已有同步任务正在运行，请稍后再试");
                return ResponseEntity.badRequest().body(result);
            }

            log.info("开始执行并行增量同步");

            // 异步执行同步任务
            new Thread(() -> {
                try {
                    syncServiceWrapper.safePerformIncrementalSync();
                } catch (Exception e) {
                    log.error("增量同步执行失败", e);
                }
            }).start();

            result.put("success", true);
            result.put("message", "并行增量同步任务已启动，请通过状态查询接口查看进度");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("启动增量同步失败", e);
            result.put("success", false);
            result.put("message", "启动增量同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 查询同步状态历史
     */
    @GetMapping("/status")
    @Operation(summary = "查询同步状态", description = "查询同步任务的执行状态和历史记录")
    public ResponseEntity<Map<String, Object>> getSyncStatus(
            @Parameter(description = "同步类型：FULL_SYNC(全量同步) / INCREMENTAL_SYNC(增量同步)，不传则查询所有")
            @RequestParam(required = false) String syncType) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<SyncStatus> syncHistory = syncServiceWrapper.getSyncHistory(syncType);
            boolean hasRunningTask = syncServiceWrapper.hasRunningSyncTask();
            
            result.put("success", true);
            result.put("hasRunningTask", hasRunningTask);
            result.put("syncHistory", syncHistory);
            result.put("message", "查询成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("查询同步状态失败", e);
            result.put("success", false);
            result.put("message", "查询同步状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 检查是否有正在运行的同步任务
     */
    @GetMapping("/running")
    @Operation(summary = "检查运行状态", description = "检查是否有正在运行的同步任务")
    public ResponseEntity<Map<String, Object>> checkRunningStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean hasRunningTask = syncServiceWrapper.hasRunningSyncTask();
            
            result.put("success", true);
            result.put("hasRunningTask", hasRunningTask);
            result.put("message", hasRunningTask ? "有同步任务正在运行" : "没有正在运行的同步任务");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("检查运行状态失败", e);
            result.put("success", false);
            result.put("message", "检查运行状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取同步统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取同步统计", description = "获取同步任务的统计信息")
    public ResponseEntity<Map<String, Object>> getSyncStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<SyncStatus> allHistory = syncServiceWrapper.getSyncHistory(null);
            
            // 统计信息
            long totalSyncTasks = allHistory.size();
            long successfulTasks = allHistory.stream()
                .filter(s -> "SUCCESS".equals(s.getSyncStatus()))
                .count();
            long failedTasks = allHistory.stream()
                .filter(s -> "FAILED".equals(s.getSyncStatus()))
                .count();
            long runningTasks = allHistory.stream()
                .filter(s -> "RUNNING".equals(s.getSyncStatus()))
                .count();

            // 最近的同步任务
            SyncStatus lastSync = allHistory.isEmpty() ? null : allHistory.get(0);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalSyncTasks", totalSyncTasks);
            statistics.put("successfulTasks", successfulTasks);
            statistics.put("failedTasks", failedTasks);
            statistics.put("runningTasks", runningTasks);
            statistics.put("lastSync", lastSync);
            
            result.put("success", true);
            result.put("statistics", statistics);
            result.put("message", "查询成功");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取同步统计失败", e);
            result.put("success", false);
            result.put("message", "获取同步统计失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取定时任务状态
     */
    @GetMapping("/scheduler/status")
    @Operation(summary = "获取定时任务状态", description = "查看定时同步任务的配置和运行状态")
    public ResponseEntity<Map<String, Object>> getSchedulerStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (text2SyncScheduler == null) {
                result.put("success", false);
                result.put("message", "定时任务功能未启用");
                result.put("schedulerEnabled", false);
                return ResponseEntity.ok(result);
            }

            String status = text2SyncScheduler.getSchedulerStatus();

            result.put("success", true);
            result.put("schedulerEnabled", true);
            result.put("status", status);
            result.put("config", syncScheduleConfig);
            result.put("message", "查询成功");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取定时任务状态失败", e);
            result.put("success", false);
            result.put("message", "获取定时任务状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 手动触发智能同步（即使定时任务正在运行）
     */
    @PostMapping("/scheduler/trigger-incremental")
    @Operation(summary = "手动触发智能同步", description = "立即执行一次智能同步，自动判断全量或增量，不受定时任务状态影响")
    public ResponseEntity<Map<String, Object>> triggerIncrementalSync() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (text2SyncScheduler == null) {
                result.put("success", false);
                result.put("message", "定时任务功能未启用，请使用普通同步接口");
                return ResponseEntity.badRequest().body(result);
            }

            // 检查是否有正在运行的任务
            if (syncServiceWrapper.hasRunningSyncTask()) {
                result.put("success", false);
                result.put("message", "已有同步任务正在运行，请稍后再试");
                return ResponseEntity.badRequest().body(result);
            }

            log.info("手动触发智能同步任务");

            // 异步执行
            new Thread(() -> {
                try {
                    text2SyncScheduler.performScheduledSmartSync();
                } catch (Exception e) {
                    log.error("手动触发智能同步失败", e);
                }
            }).start();

            result.put("success", true);
            result.put("message", "智能同步任务已手动触发");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("手动触发增量同步失败", e);
            result.put("success", false);
            result.put("message", "手动触发增量同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取定时任务配置信息
     */
    @GetMapping("/scheduler/config")
    @Operation(summary = "获取定时任务配置", description = "查看当前的定时任务配置参数")
    public ResponseEntity<Map<String, Object>> getSchedulerConfig() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (syncScheduleConfig == null) {
                result.put("success", false);
                result.put("message", "定时任务配置未找到");
                return ResponseEntity.ok(result);
            }

            result.put("success", true);
            result.put("config", syncScheduleConfig);
            result.put("message", "查询成功");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取定时任务配置失败", e);
            result.put("success", false);
            result.put("message", "获取定时任务配置失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取同步策略
     */
    @GetMapping("/strategy")
    @Operation(summary = "获取同步策略", description = "查看当前的智能同步策略")
    public ResponseEntity<Map<String, Object>> getSyncStrategy() {
        Map<String, Object> result = new HashMap<>();

        try {
            String strategy = syncServiceWrapper.getSyncStrategy();
            var stats = syncServiceWrapper.getSyncHistoryStats();

            result.put("success", true);
            result.put("strategy", strategy);
            result.put("stats", stats);
            result.put("message", "查询成功");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取同步策略失败", e);
            result.put("success", false);
            result.put("message", "获取同步策略失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 手动触发智能同步（即使定时任务正在运行）
     */
    @PostMapping("/scheduler/trigger-smart")
    @Operation(summary = "手动触发智能同步", description = "立即执行一次智能同步，不受定时任务状态影响")
    public ResponseEntity<Map<String, Object>> triggerSmartSync() {
        Map<String, Object> result = new HashMap<>();

        try {
            if (text2SyncScheduler == null) {
                result.put("success", false);
                result.put("message", "定时任务功能未启用，请使用普通同步接口");
                return ResponseEntity.badRequest().body(result);
            }

            // 检查是否有正在运行的任务
            if (syncServiceWrapper.hasRunningSyncTask()) {
                result.put("success", false);
                result.put("message", "已有同步任务正在运行，请稍后再试");
                return ResponseEntity.badRequest().body(result);
            }

            log.info("手动触发智能同步任务");

            // 异步执行
            new Thread(() -> {
                try {
                    text2SyncScheduler.performScheduledSmartSync();
                } catch (Exception e) {
                    log.error("手动触发智能同步失败", e);
                }
            }).start();

            result.put("success", true);
            result.put("message", "智能同步任务已手动触发");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("手动触发智能同步失败", e);
            result.put("success", false);
            result.put("message", "手动触发智能同步失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

}
