package com.bzlj.message.sync.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 同步定时任务配置属性
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Data
@Component
@ConfigurationProperties(prefix = "sync.schedule")
public class SyncScheduleConfig {

    /**
     * 是否启用定时同步
     */
    private boolean enabled = false;

    /**
     * 同步任务的 cron 表达式
     * 默认每分钟执行一次：0 * * * * ?
     */
    private String syncCron = "0 * * * * ?";

    /**
     * 同步任务超时时间（分钟）
     */
    private int timeoutMinutes = 30;

    /**
     * 是否允许并发执行
     */
    private boolean allowConcurrent = false;

    /**
     * 失败重试次数
     */
    private int retryCount = 3;

    /**
     * 重试间隔（秒）
     */
    private int retryIntervalSeconds = 60;

    /**
     * 是否启用并行同步
     */
    private boolean parallelEnabled = true;

    /**
     * 并行同步页面大小
     */
    private int pageSize = 500;

    /**
     * 最大并发页数
     */
    private int maxConcurrentPages = 10;
}
