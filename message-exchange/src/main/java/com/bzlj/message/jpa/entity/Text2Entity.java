package com.bzlj.message.jpa.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;

/**
 * Text2 表实体类
 * 对应数据库表：text2
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Data
@Entity
@Table(name = "text2", schema = "bwtys0", indexes = {
    @Index(name = "text2_tc_no_idx", columnList = "tc_no, tc_item_seq_no, loop_count")
})
@IdClass(Text2Id.class)
@EqualsAndHashCode(callSuper = false)
public class Text2Entity {

    /**
     * 记录创建者
     */
    @Column(name = "rec_creator", nullable = false, columnDefinition = "varchar default ' '")
    private String recCreator = " ";

    /**
     * 记录创建时间
     */
    @Column(name = "rec_create_time", nullable = false, columnDefinition = "varchar default ' '")
    private String recCreateTime = " ";

    /**
     * 记录修改者
     */
    @Column(name = "rec_revisor", nullable = false, columnDefinition = "varchar default ' '")
    private String recRevisor = " ";

    /**
     * 记录修改时间
     */
    @Column(name = "rec_revise_time", nullable = false, columnDefinition = "varchar default ' '")
    private String recReviseTime = " ";

    /**
     * 归档标志
     */
    @Column(name = "archive_flag", nullable = false, columnDefinition = "varchar default ' '")
    private String archiveFlag = " ";

    /**
     * TC编号 (主键)
     */
    @Id
    @Column(name = "tc_no", nullable = false, columnDefinition = "varchar default ' '")
    private String tcNo = " ";

    /**
     * TC项目序号
     */
    @Column(name = "tc_item_seq_no", nullable = false, columnDefinition = "integer default 0")
    private Integer tcItemSeqNo = 0;

    /**
     * TC项目名称 (主键)
     */
    @Id
    @Column(name = "tc_item_name", nullable = false, columnDefinition = "varchar default ' '")
    private String tcItemName = " ";

    /**
     * TC项目类型
     */
    @Column(name = "tc_item_type", nullable = false, columnDefinition = "varchar default ' '")
    private String tcItemType = " ";

    /**
     * TC项目长度
     */
    @Column(name = "tc_item_len", nullable = false, columnDefinition = "integer default 0")
    private Integer tcItemLen = 0;

    /**
     * TC项目小数位数
     */
    @Column(name = "tc_item_dec", nullable = false, columnDefinition = "integer default 0")
    private Integer tcItemDec = 0;

    /**
     * TC项目默认值
     */
    @Column(name = "tc_item_default_value", nullable = false, columnDefinition = "varchar default ' '")
    private String tcItemDefaultValue = " ";

    /**
     * 循环次数
     */
    @Column(name = "loop_count", nullable = false, columnDefinition = "integer default 0")
    private Integer loopCount = 0;

    /**
     * 段循环标志
     */
    @Column(name = "segment_loop_flag", nullable = false, columnDefinition = "varchar default ' '")
    private String segmentLoopFlag = " ";

    /**
     * 关键字标志
     */
    @Column(name = "key_flag", nullable = false, columnDefinition = "varchar default ' '")
    private String keyFlag = " ";

    /**
     * 表索引
     */
    @Column(name = "table_index", nullable = false, columnDefinition = "integer default 0")
    private Integer tableIndex = 0;

    /**
     * 对齐类型
     */
    @Column(name = "align_type", nullable = false, columnDefinition = "varchar default ' '")
    private String alignType = " ";

    /**
     * 填充字符
     */
    @Column(name = "padding_char", nullable = false, columnDefinition = "varchar default ' '")
    private String paddingChar = " ";

    /**
     * 无效策略
     */
    @Column(name = "invalid_strategy", nullable = false, columnDefinition = "varchar default ' '")
    private String invalidStrategy = " ";

    /**
     * 备注
     */
    @Column(name = "remark", nullable = false, columnDefinition = "varchar default ' '")
    private String remark = " ";
}
