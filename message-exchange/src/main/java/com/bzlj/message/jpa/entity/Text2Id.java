package com.bzlj.message.jpa.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * Text2 表复合主键类
 * 主键字段：tc_no, tc_item_name
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Text2Id implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * TC编号
     */
    private String tcNo;

    /**
     * TC项目名称
     */
    private String tcItemName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Text2Id text2Id = (Text2Id) o;
        return Objects.equals(tcNo, text2Id.tcNo) &&
               Objects.equals(tcItemName, text2Id.tcItemName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tcNo, tcItemName);
    }
}
