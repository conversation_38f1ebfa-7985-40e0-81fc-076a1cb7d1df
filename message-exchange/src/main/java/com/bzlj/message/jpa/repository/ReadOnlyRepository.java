package com.bzlj.message.jpa.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.List;
import java.util.Optional;

/**
 * 只读 Repository 基接口
 * 只提供查询方法，不提供写操作方法
 * 适用于只读权限的数据库连接
 *
 * @param <T>  实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @date 2025/8/2
 */
@NoRepositoryBean
public interface ReadOnlyRepository<T, ID> {

    /**
     * 根据ID查找实体
     */
    Optional<T> findById(ID id);

    /**
     * 检查实体是否存在
     */
    boolean existsById(ID id);

    /**
     * 查找所有实体
     */
    List<T> findAll();

    /**
     * 根据排序查找所有实体
     */
    List<T> findAll(Sort sort);

    /**
     * 分页查找所有实体
     */
    Page<T> findAll(Pageable pageable);

    /**
     * 根据ID列表查找实体
     */
    List<T> findAllById(Iterable<ID> ids);

    /**
     * 统计实体总数
     */
    long count();
}
