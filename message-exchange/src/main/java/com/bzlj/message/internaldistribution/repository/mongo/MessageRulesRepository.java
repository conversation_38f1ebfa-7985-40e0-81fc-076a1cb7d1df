package com.bzlj.message.internaldistribution.repository.mongo;

import com.bzlj.message.internaldistribution.entity.mongo.MessageRules;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface MessageRulesRepository extends MongoRepository<MessageRules, String> {

    List<MessageRules> findByTelegraphTextCode(String telegraphTextCode);

    /**
     * 根据电文号和字段名查找记录（用于同步时检查是否已存在）
     *
     * @param telegraphTextCode 电文号
     * @param field            字段名
     * @return 记录
     */
    Optional<MessageRules> findByTelegraphTextCodeAndField(String telegraphTextCode, String field);

}
