package com.bzlj.message.internaldistribution.entity.mongo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 同步状态记录实体
 * 用于记录 Text2 到 MessageRules 的同步状态
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Data
@Accessors(chain = true)
@Document(collection = "sync_status")
public class SyncStatus implements Serializable {

    @Id
    private String id;

    /**
     * 同步类型：FULL_SYNC(全量同步) / INCREMENTAL_SYNC(增量同步)
     */
    @Field("sync_type")
    private String syncType;

    /**
     * 同步状态：RUNNING(进行中) / SUCCESS(成功) / FAILED(失败)
     */
    @Field("sync_status")
    private String syncStatus;

    /**
     * 同步开始时间
     */
    @Field("start_time")
    private LocalDateTime startTime;

    /**
     * 同步结束时间
     */
    @Field("end_time")
    private LocalDateTime endTime;

    /**
     * 最后同步的创建时间（用于增量同步基准）
     */
    @Field("last_sync_create_time")
    private String lastSyncCreateTime;

    /**
     * 同步的记录总数
     */
    @Field("total_records")
    private Long totalRecords;

    /**
     * 成功处理的记录数
     */
    @Field("success_records")
    private Long successRecords;

    /**
     * 失败的记录数
     */
    @Field("failed_records")
    private Long failedRecords;

    /**
     * 新增的记录数
     */
    @Field("inserted_records")
    private Long insertedRecords;

    /**
     * 更新的记录数
     */
    @Field("updated_records")
    private Long updatedRecords;

    /**
     * 跳过的记录数（无变化）
     */
    @Field("skipped_records")
    private Long skippedRecords;

    /**
     * 错误信息
     */
    @Field("error_message")
    private String errorMessage;

    /**
     * 同步耗时（毫秒）
     */
    @Field("duration_ms")
    private Long durationMs;

    /**
     * 备注信息
     */
    @Field("remark")
    private String remark;

    /**
     * 创建时间
     */
    @Field("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Field("update_time")
    private LocalDateTime updateTime;
}
