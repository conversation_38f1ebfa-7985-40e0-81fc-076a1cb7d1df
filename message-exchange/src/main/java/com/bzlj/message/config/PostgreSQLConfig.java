package com.bzlj.message.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * PostgreSQL 条件化配置类
 * 只有当 postgresql.enabled=true 时才会启用 JPA 相关功能
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
@EnableJpaRepositories(basePackages = {"com.bzlj.message.jpa.repository"})
@EntityScan(basePackages = {"com.bzlj.message.jpa.entity"})
public class PostgreSQLConfig {

    public PostgreSQLConfig() {
        log.info("PostgreSQL 功能已启用，JPA Repository 和 Entity 扫描已配置");
    }
}
