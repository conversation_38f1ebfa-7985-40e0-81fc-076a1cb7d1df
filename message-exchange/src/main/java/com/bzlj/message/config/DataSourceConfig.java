package com.bzlj.message.config;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据源条件化配置类
 * 只有当 postgresql.enabled=true 时才会启用数据源配置
 * 配置为只读模式，适用于只读权限的数据库账号
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
public class DataSourceConfig {

    @Value("${postgresql.datasource.host:localhost}")
    private String host;

    @Value("${postgresql.datasource.port:5432}")
    private String port;

    @Value("${postgresql.datasource.database:gyck}")
    private String database;

    @Value("${postgresql.datasource.schema:bwtys0}")
    private String schema;

    @Value("${postgresql.datasource.username:gyck_viewer}")
    private String username;

    @Value("${postgresql.datasource.password:H?4RLv53iamJq+!}")
    private String password;

    public DataSourceConfig() {
        log.info("PostgreSQL 只读数据源配置已启用");
        log.warn("注意：当前配置为只读模式，不支持写操作");
    }

    /**
     * 配置只读数据源
     * 设置连接为只读模式，防止意外的写操作
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
    public DataSource readOnlyDataSource() {
        log.info("创建只读 PostgreSQL 数据源");

        HikariDataSource dataSource = new HikariDataSource();

        // 基本连接配置
        String jdbcUrl = String.format("*******************************************", host, port, database, schema);
        dataSource.setJdbcUrl(jdbcUrl);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDriverClassName("org.postgresql.Driver");

        // 连接池配置
        dataSource.setMaximumPoolSize(10);
        dataSource.setMinimumIdle(2);
        dataSource.setConnectionTimeout(30000);
        dataSource.setIdleTimeout(600000);
        dataSource.setMaxLifetime(1800000);

        // 只读配置
        dataSource.setReadOnly(true);
        dataSource.setAutoCommit(true);  // 只读模式下使用自动提交

        // 连接验证
        dataSource.setConnectionTestQuery("SELECT 1");
        dataSource.setValidationTimeout(5000);

        log.info("PostgreSQL 数据源配置完成: {}, schema: {}", jdbcUrl, schema);

        return dataSource;
    }
}
