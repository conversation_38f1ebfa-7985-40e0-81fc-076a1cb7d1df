package com.bzlj.message.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * PostgreSQL 配置属性类
 * 用于管理 PostgreSQL 功能的启用状态
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Data
@Component
@ConfigurationProperties(prefix = "postgresql")
public class PostgreSQLProperties {

    /**
     * 是否启用 PostgreSQL 功能
     * 默认为 false，需要显式启用
     */
    private boolean enabled = false;
}
