package com.bzlj.message.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 事务管理配置
 * 分别配置 MongoDB 和 PostgreSQL 的事务管理器
 *
 * <AUTHOR>
 * @date 2025/8/2
 */
@Slf4j
@Configuration
@EnableTransactionManagement
public class TransactionConfig {

    /**
     * MongoDB 事务管理器（主要）
     */
    @Bean
    @Primary
    public PlatformTransactionManager mongoTransactionManager(MongoTemplate mongoTemplate) {
        log.info("配置 MongoDB 事务管理器（主要）");
        return new MongoTransactionManager(mongoTemplate.getMongoDatabaseFactory());
    }

    /**
     * PostgreSQL 事务管理器（条件化）
     * 只有在 PostgreSQL 启用时才创建
     */
    @Bean("postgresqlTransactionManager")
    @ConditionalOnProperty(name = "postgresql.enabled", havingValue = "true")
    public PlatformTransactionManager postgresqlTransactionManager(DataSource dataSource) {
        log.info("配置 PostgreSQL 事务管理器（只读）");
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setDataSource(dataSource);
        
        // 设置为只读事务管理器
        transactionManager.setDefaultTimeout(30);
        transactionManager.setRollbackOnCommitFailure(true);
        
        return transactionManager;
    }
}
