package com.bzlj.base.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 查询实体
 * <AUTHOR>
 * @description:
 * @date 2025-03-06 16:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class SearchCondition implements Serializable {
    /**
     * 查询参数
     */
    @JsonProperty("searchItems")
    protected SearchItems searchItems;



//    @JsonProperty("distinctFields")
//    private List<String> distinctFields;


    /**
     * 起始页
     */
    @JsonProperty("pageCurrent")
    private Integer pageCurrent = 1;

    /**
     * 每页条数
     */
    @JsonProperty("pageSize")
    private Integer pageSize = 10;

    /**
     * 排序
     */
    @JsonProperty("sortItems")
    List<SortItem> sortItems;

    /**
     * 关联表字段
     */
    @JsonProperty("openProps")
    List<String> openProps;
}
