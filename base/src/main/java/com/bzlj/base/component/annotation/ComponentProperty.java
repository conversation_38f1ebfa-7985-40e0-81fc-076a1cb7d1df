package com.bzlj.base.component.annotation;

import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-07 9:47
 */
@Target({ElementType.FIELD})
@Retention(RUNTIME)
public @interface ComponentProperty {
    /**
     * 标签
     * @return
     */
    String label();

    /**
     * 是否可搜索
     * @return
     */
    boolean searchable() default false;

    /**
     * 排序
     * @return
     */
    int order() default 0;

    /**
     * 字典code
     * @return
     */
    String dictCode() default "";

    /**
     * 是否生效
     * @return
     */
    boolean enable() default true;

    /**
     * 页面是否展示
     * @return
     */
    boolean display() default true;

    /**
     * 属性路径
     * @return
     */
    String path();

    /**
     * 格式化
     * @return
     */
    String format() default "YYYY-MM-DD HH:mm:ss";

    /**
     * 是否范围查询
     * @return
     */
    boolean range() default false;

    String alias() default "";

    Class<?> subClass() default void.class;

    boolean multiple() default false;

    /**
     * 数据源类型
     */
    DataSourceType dataSourceType() default DataSourceType.STATIC;

    DataSource dataSource() default @DataSource();

    boolean initialRequest() default false;

    boolean excludeFromRequest() default false;

    String[] dependencies() default {};

    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.TYPE)
    @interface DataSource {
        /**
         * API接口URL（当dataSourceType为API时使用）
         */
        String apiUrl() default "";

        /**
         * HTTP方法（当dataSourceType为API时使用）
         */
        HttpMethod httpMethod() default HttpMethod.GET;

        /**
         * API请求参数，支持多种参数类型
         */
        ApiParam[] apiParams() default {};

        /**
         * 值字段映射
         */
        Mapping mapping() default @Mapping;
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.TYPE)
    @interface ApiParam {
        /**
         * 参数类型，如：params、dependencyParams、apiParams等
         */
        String key();

        /**
         * 参数值（JSON字符串格式）
         */
        String value();
    }

    @Retention(RetentionPolicy.RUNTIME)
    @Target(ElementType.TYPE)
    @interface Mapping {
        String valueKey() default "";
        String labelKey() default "";
    }
}
