package com.bzlj.base.service;

import com.bzlj.base.querydsl.builder.QuerydslPredicateBuilder;
import com.bzlj.base.querydsl.fetch.QuerydslJoinFetcher;
import com.bzlj.base.querydsl.sort.QuerydslSortBuilder;
import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.result.PageResult;
import com.bzlj.base.search.SearchCondition;
import com.google.common.collect.Lists;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-07 17:36
 */
public interface IBaseService<E, T, ID extends Serializable> {


    @Autowired
    BaseRepository<E, ID> getRepository();

    @PersistenceContext
    EntityManager getEntityManager();

    Map<Class<?>, Constructor<?>> constructorCache = new ConcurrentHashMap<>();

    Class<T> getDTOClass();

    Class<E> getPOClass();

    /**
     * 查询
     *
     * @param id
     * @return
     */
    default T find(ID id) {
        E entity = getRepository().findById(id).orElse(null);
        if(Objects.isNull(entity)) return null;
        return convertToDto(entity,getDTOClass());
    }

    default List<T> findList(List<ID> ids) {
        List<E> all = getRepository().findAllById(ids);
        if(CollectionUtils.isEmpty(all)) return Lists.newArrayList();
        return convertToDtoList(all,getDTOClass());
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    default void delete(ID id) {
        getRepository().deleteById(id);
    }

    default void batchDelete(List<ID> ids) {
        getRepository().deleteAllById(ids);
    }

    /**
     * 创建
     *
     * @param entity
     * @return
     */
    default T insert(E entity) {
        //todo 后续添加新增前、新增后事件
        E save = getRepository().save(entity);
        return convertToDto(save,getDTOClass());
    }

    default E insertEntity(E entity) {
        //todo 后续添加新增前、新增后事件
        return getRepository().save(entity);
    }

    default List<T> batchInsert(List<E> entities) {
        return convertToDtoList(batchInsertEntity(entities),getDTOClass());
    }

    default List<E> batchInsertEntity(List<E> entities) {
        return getRepository().saveAll(entities);
    }

    /**
     * 更新
     *
     * @param entity
     * @return
     */
    default T update(E entity) {
        //todo 后续添加更改前、更改后事件
        return convertToDto(updateEntity(entity),getDTOClass());
    }

    default E updateEntity(E entity) {
        //todo 后续添加更改前、更改后事件
        return getRepository().save(entity);
    }

    default List<T> batchUpdate(List<E> entities) {
        return convertToDtoList(batchUpdateEntity(entities),getDTOClass());
    }

    default List<E> batchUpdateEntity(List<E> entities) {
        return getRepository().saveAll(entities);
    }

    /**
     * 读取所有
     *
     * @param pageable
     * @return
     */
    default Page<T> page(Pageable pageable) {
        Page<E> all = getRepository().findAll(pageable);
        if(Objects.isNull(all) || CollectionUtils.isEmpty(all.getContent())){
            return Page.empty();
        }
        return new PageImpl(convertToDtoList(all.getContent(),getDTOClass()),all.getPageable(),all.getTotalElements());
    }

    /**
     * 判断id是否存在
     *
     * @param id
     * @return
     */
    default boolean exists(ID id) {
        return getRepository().existsById(id);
    }

    default List<T> findAll() {
        List<E> all = getRepository().findAll();
        return convertToDtoList(all,getDTOClass());
    }

    default JPAQuery<E> initJPAQuery(String rootAlias) {
        JPAQuery<E> query = new JPAQuery<>(getEntityManager());
        PathBuilder<E> rootPath = new PathBuilder<>(getPOClass(), rootAlias);

        // 1. 初始化 FROM 子句
        query.from(rootPath).select(rootPath);
        return query;
    }

    private JPAQuery<E>  buildOtherCondition(JPAQuery<E> query, SearchCondition condition, String rootAlias) {
        // 3. 自动处理嵌套关联加载
        new QuerydslJoinFetcher<>(query, getPOClass(), rootAlias).fetchJoins(condition.getOpenProps());
        // 4. 动态排序
        QuerydslSortBuilder<E> sortBuilder =
                new QuerydslSortBuilder<>(getPOClass(), rootAlias);
        List<OrderSpecifier<?>> orders = sortBuilder.buildSort(condition.getSortItems());
        if (!CollectionUtils.isEmpty(orders)) {
            query.orderBy(orders.toArray(new OrderSpecifier[0]));
        }
        return query;
    }

    @Transactional(rollbackFor = Exception.class)
    default PageResult<T> findByCondition(SearchCondition condition) {
        String rootAlias = "entity";
        JPAQuery<E> query = initJPAQuery(rootAlias);

        // 2. 动态构建查询条件
        QuerydslPredicateBuilder<E> predicateBuilder =
                new QuerydslPredicateBuilder<>(getPOClass(), condition, rootAlias);
        Predicate predicate = predicateBuilder.build();
        query.where(predicate);
        long total = query.fetchCount(); // 获取总数
        // 3. 自动处理嵌套关联加载
        buildOtherCondition(query,condition,rootAlias);
        int offset = (condition.getPageCurrent() - 1) * condition.getPageSize();
        // 执行查询
        List<E> result = query.offset(offset)
                .limit(condition.getPageSize())
                .fetch();
        return PageResult.of(condition, total, convertToDtoList(result,getDTOClass()));
    }


    /*private Expression<E>[] addAggregationType(SearchCondition condition) {
        List<Expression<E>> expressions = new ArrayList<>();
        if(CollectionUtils.isEmpty(condition.getAggregationItems())) return null;
        condition.getAggregationItems().forEach(agg -> {
            Expression<E> expression = getExpression(agg.fieldName);
            Ops.AggOps aggOps = null;
            Class clsType = null;
            switch (agg.aggregationType) {
                case AVG:
                    aggOps = Ops.AggOps.AVG_AGG;
                    clsType = expression.getType();
                    break;
                case MAX:
                    aggOps = Ops.AggOps.MAX_AGG;
                    clsType = expression.getType();
                    break;
                case MIN:
                    aggOps = Ops.AggOps.MIN_AGG;
                    clsType = expression.getType();
                    break;
                case SUM:
                    aggOps = Ops.AggOps.SUM_AGG;
                    clsType = expression.getType();
                    break;
                case COUNT:
                    aggOps = Ops.AggOps.COUNT_AGG;
                    clsType = Long.class;
                    break;
                default:
            }
            if (Objects.nonNull(aggOps)) {
                expressions.add(ExpressionUtils.operation(clsType, aggOps, expression));
            }
        });
        return expressions.toArray(new Expression[0]);
    }

    // 动态获取字段表达式
    private Expression<E> getExpression(String fieldName) {
        try {
            PathBuilder<E> rootPath = new PathBuilder<>(getPOClass(), "entity");
            return (Expression<E>) PathUtil.resolvePath(rootPath, fieldName);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid field name: " + fieldName);
        }
    }*/


    @Transactional(rollbackFor = Exception.class)
    default List<T> findAllWithCondition(SearchCondition condition) {
        return convertToDtoList(findAllEntityWithCondition(condition),getDTOClass());
    }

    @Transactional(rollbackFor = Exception.class)
    default List<E> findAllEntityWithCondition(SearchCondition condition) {
        String rootAlias = "entity";
        JPAQuery<E> query = initJPAQuery(rootAlias);
        // 2. 动态构建查询条件
        QuerydslPredicateBuilder<E> predicateBuilder =
                new QuerydslPredicateBuilder<>(getPOClass(), condition, rootAlias);
        Predicate predicate = predicateBuilder.build();
        query.where(predicate);
        buildOtherCondition(query,condition,rootAlias);
        return query.fetch();
    }

    @Transactional(rollbackFor = Exception.class)
    default E findOne(SearchCondition condition) {
        condition.setPageCurrent(1);
        condition.setPageSize(1);
        List<E> all = findAllEntityWithCondition(condition);
        if(CollectionUtils.isEmpty(all)) return null;
        return all.getFirst();
    }


    @Transactional(rollbackFor = Exception.class)
    default List<E> findEntityWithCondition(SearchCondition condition) {
        String rootAlias = "entity";
        JPAQuery<E> query = initJPAQuery(rootAlias);
        // 2. 动态构建查询条件
        QuerydslPredicateBuilder<E> predicateBuilder =
                new QuerydslPredicateBuilder<>(getPOClass(), condition, rootAlias);
        Predicate predicate = predicateBuilder.build();
        query.where(predicate);
        buildOtherCondition(query,condition,rootAlias);
        List<E> fetch = query.fetch();
        if(CollectionUtils.isEmpty(fetch)) fetch = new ArrayList<>();
        return fetch;
    }


    default <R> R createInstance(Class<R> clazz) {
        Constructor<?> constructor = constructorCache.computeIfAbsent(clazz, c -> {
            try {
                return c.getDeclaredConstructor();
            } catch (NoSuchMethodException e) {
                throw new IllegalArgumentException(clazz.getName() + " 缺少无参构造函数");
            }
        });
        try {
            return clazz.cast(constructor.newInstance());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 默认方法：将实体对象 E 转换为 DTO 对象 T
     *
     * @param entity   实体对象
     * @param dtoClass DTO 的 Class 类型
     * @return 转换后的 DTO 对象
     */
    default T convertToDto(E entity, Class<T> dtoClass,String... ignoreProperties) {
        try {
            // 1. 通过反射实例化目标 DTO
            T dto = createInstance(dtoClass);
            // 2. 复制属性（要求 E 和 T 的字段名和类型匹配）
            BeanUtils.copyProperties(entity, dto,ignoreProperties);

            return dto;
        } catch (Exception e) {
            throw new RuntimeException("对象转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 默认方法：将 DTO 对象 T 转换为实体对象 E
     *
     * @param dto         DTO 对象
     * @param entityClass 实体的 Class 类型
     * @return 转换后的实体对象
     */
    default E convertToEntity(T dto, Class<E> entityClass,String... ignoreProperties) {
        try {
            E entity = createInstance(entityClass);
            BeanUtils.copyProperties(dto, entity,ignoreProperties);
            return entity;
        } catch (Exception e) {
            throw new RuntimeException("对象转换失败: " + e.getMessage(), e);
        }
    }

    default List<T> convertToDtoList(List<E> entities, Class<T> dtoClass) {
        if (entities == null || entities.isEmpty()) {
            return Collections.emptyList();
        }
        return entities.stream()
                .map(entity -> convertToDto(entity, dtoClass))
                .collect(Collectors.toList());
    }

    default List<E> convertToEntityList(List<T> dtos, Class<E> entityClass) {
        if (dtos == null || dtos.isEmpty()) {
            return Collections.emptyList();
        }
        return dtos.stream()
                .map(dto -> convertToEntity(dto, entityClass))
                .collect(Collectors.toList());
    }
}
