package com.bzlj.base.util;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Slf4j
public class DTOConverter {


    // 获取带顺序的VO属性列表
    private static List<Field> getOrderedFields(Class<?> voClass) {
        List<Field> fields = new ArrayList<>();
        
        // 获取所有声明字段
        Field[] allFields = voClass.getDeclaredFields();
        for (Field field : allFields) {
            if (field.isAnnotationPresent(ComponentProperty.class)) {
                fields.add(field);
            }
        }
        
        // 按注解顺序和声明位置排序
        fields.sort(Comparator
            .comparingInt((Field f) -> f.getAnnotation(ComponentProperty.class).order())
            .thenComparingInt(f -> getFieldIndex(f, allFields)));
        
        return fields;
    }

    // 获取字段在类中的声明顺序
    private static int getFieldIndex(Field target, Field[] allFields) {
        for (int i = 0; i < allFields.length; i++) {
            if (allFields[i].equals(target)) {
                return i;
            }
        }
        return -1;
    }

    public static List<List<Object>> convert(List<?> dtoList, Class<?> voClass) {
        List<Field> orderedFields = getOrderedFields(voClass);
        List<List<Object>> result = new ArrayList<>();

        // 初始化结果集合
        for (int i = 0; i < orderedFields.size(); i++) {
            result.add(new ArrayList<>());
        }

        // 填充数据
        for (Object dto : dtoList) {
            for (int i = 0; i < orderedFields.size(); i++) {
                Field voField = orderedFields.get(i);
                try {
                    Field dtoField = dto.getClass().getDeclaredField(voField.getName());
                    dtoField.setAccessible(true);
                    Object value = dtoField.get(dto);

                    ComponentProperty cp = voField.getAnnotation(ComponentProperty.class);
                    if (cp != null && cp.subClass() != void.class) {
                        // 递归处理嵌套VO
                        List<?> subList = (List<?>) value;
                        List<List<Object>> converted = convert(subList, cp.subClass());
                        result.get(i).add(converted);
                    } else {
                        if(value.getClass().isAssignableFrom(LocalDateTime.class)){
                            //todo 应该使用注解上的format格式，但是前端需要YYYY-MM-DD的形式，后端使用会导致时间不对
                            result.get(i).add(LocalDateTimeUtil.format((LocalDateTime) value, "yyyy-MM-dd HH:mm:ss"));
                        }else{
                            result.get(i).add(value);
                        }
                    }
                } catch (Exception e) {
                    log.error("字段映射失败: " + voField.getName(), e);
                }
            }
        }
        return result;
    }


    public static List<List<Object>> convertWithKey(List<?> dtoList, Class<?> voClass) {
        List<Field> orderedFields = getOrderedFields(voClass);
        List<List<Object>> result = new ArrayList<>();

        // 初始化结果集合
        for (int i = 0; i < orderedFields.size(); i++) {
            result.add(new ArrayList<>());
        }

        // 填充数据
        for (Object dto : dtoList) {
            for (int i = 0; i < orderedFields.size(); i++) {
                Field voField = orderedFields.get(i);
                try {
                    Field dtoField = dto.getClass().getDeclaredField(voField.getName());
                    dtoField.setAccessible(true);
                    Object value = dtoField.get(dto);

                    ComponentProperty cp = voField.getAnnotation(ComponentProperty.class);
                    String label = cp.label();
                    if(CollectionUtils.isEmpty(result.get(i))){
                        result.get(i).add(label);
                    }
                    if (cp.subClass() != void.class) {
                        // 递归处理嵌套VO
                        List<?> subList = (List<?>) value;
                        List<List<Object>> converted = convert(subList, cp.subClass());
                        result.get(i).add(converted);
                    } else {
                        if(value.getClass().isAssignableFrom(LocalDateTime.class)){
                            //todo 应该使用注解上的format格式，但是前端需要YYYY-MM-DD的形式，后端使用会导致时间不对
                            result.get(i).add(LocalDateTimeUtil.format((LocalDateTime) value, "yyyy-MM-dd HH:mm:ss"));
                        }else{
                            result.get(i).add(value);
                        }
                    }
                } catch (Exception e) {
                    log.error("字段映射失败: " + voField.getName(), e);
                }
            }
        }
        return result;
    }

}